# -*- coding: utf-8 -*-

"""
测试新的眨眼检测逻辑
"""

import math
import sys
import os

# 模拟Maya的OpenMaya模块（用于测试）
class MockMMatrix:
    def __init__(self, values=None):
        if values is None:
            values = [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]
        self.values = values

    def __getitem__(self, index):
        return self.values[index]

# 创建模拟的OpenMaya模块
class MockOpenMaya:
    class MMatrix:
        def __init__(self, values=None):
            if values is None:
                values = [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]
            self.values = values

        def __getitem__(self, index):
            return self.values[index]

    class MTransformationMatrix:
        def __init__(self, matrix):
            self.matrix = matrix

        def eulerRotation(self):
            # 简化的欧拉角提取（仅用于测试）
            return MockEulerRotation()

class MockEulerRotation:
    def __init__(self):
        # 从矩阵中提取简化的旋转信息
        self.x = 0.5  # 示例值
        self.y = 0.0
        self.z = 0.0

# 将模拟模块添加到sys.modules
sys.modules['maya'] = type('maya', (), {})()
sys.modules['maya.OpenMaya'] = MockOpenMaya()

from analysis_eyeball_rotation import AnalysisEyeballRotation


def create_test_data():
    """
    创建测试数据：模拟一个完整的眨眼序列
    """
    frame_matrix_dict = {}
    
    # 创建一个更复杂的测试序列
    # 帧1-5: 正常状态（无旋转）
    for frame in range(1, 6):
        frame_matrix_dict[frame] = MockOpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])

    # 帧6-10: 第一次眨眼 - 开始旋转（超过阈值）
    angles = [20, 35, 45, 40, 25]  # 角度变化
    for i, frame in enumerate(range(6, 11)):
        angle_rad = math.radians(angles[i])
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        frame_matrix_dict[frame] = MockOpenMaya.MMatrix([1, 0, 0, 0, 0, cos_a, -sin_a, 0, 0, sin_a, cos_a, 0, 0, 0, 0, 1])

    # 帧11-15: 恢复正常（低于阈值）
    for frame in range(11, 16):
        frame_matrix_dict[frame] = MockOpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])

    # 帧16-20: 冷却期（应该不检测）
    angles = [25, 30, 35, 30, 20]  # 即使有旋转也不应该检测
    for i, frame in enumerate(range(16, 21)):
        angle_rad = math.radians(angles[i])
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        frame_matrix_dict[frame] = MockOpenMaya.MMatrix([1, 0, 0, 0, 0, cos_a, -sin_a, 0, 0, sin_a, cos_a, 0, 0, 0, 0, 1])

    # 帧21-25: 恢复正常
    for frame in range(21, 26):
        frame_matrix_dict[frame] = MockOpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])

    # 帧26-30: 第二次眨眼（冷却期结束后）
    angles = [30, 40, 50, 35, 20]
    for i, frame in enumerate(range(26, 31)):
        angle_rad = math.radians(angles[i])
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        frame_matrix_dict[frame] = MockOpenMaya.MMatrix([1, 0, 0, 0, 0, cos_a, -sin_a, 0, 0, sin_a, cos_a, 0, 0, 0, 0, 1])

    # 帧31-35: 恢复正常
    for frame in range(31, 36):
        frame_matrix_dict[frame] = MockOpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])
    
    return frame_matrix_dict


def test_new_logic():
    """
    测试新的眨眼检测逻辑
    """
    print("=== 测试新的眨眼检测逻辑 ===")
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 创建分析器
    analyzer = AnalysisEyeballRotation()
    analyzer.frame_matrix_dict = test_data
    analyzer.frame_spacing = 2  # n=2
    analyzer.angle_threshold = 15  # 15度阈值
    
    print(f"测试参数:")
    print(f"- 帧间隔(n): {analyzer.frame_spacing}")
    print(f"- 角度阈值: {analyzer.angle_threshold}度")
    print(f"- 冷却期: {2 * analyzer.frame_spacing}帧")
    print(f"- 总帧数: {len(test_data)}")
    print()
    
    # 执行分析
    result = analyzer.analyze_rotation_changes()
    
    print("\n=== 预期结果分析 ===")
    print("预期应该检测到:")
    print("- 第一次眨眼: 某帧输出0（开始），某帧输出1（结束）")
    print("- 冷却期: 4帧内不应有任何输出")
    print("- 第二次眨眼: 冷却期结束后，某帧输出0（开始），某帧输出1（结束）")
    
    # 打印摘要
    analyzer.print_analysis_summary()
    
    return result


if __name__ == "__main__":
    test_new_logic()
