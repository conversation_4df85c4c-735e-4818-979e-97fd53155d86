# -*- coding: utf-8 -*-

"""
随机闭眼功能测试脚本

用于测试analysis_eyeball_rotation.py中新增的随机闭眼功能
"""

import math
from maya import OpenMaya
from .analysis_eyeball_rotation import AnalysisEyeballRotation


def test_random_blink_generation():
    """
    测试随机闭眼生成功能
    """
    print("\n=== 测试随机闭眼生成功能 ===")
    
    # 创建测试矩阵数据（模拟一个简单的眨眼动作）
    frame_matrix_dict = {
        1: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]),
        10: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.707, -0.707, 0, 0, 0.707, 0.707, 0, 0, 0, 0, 1]),  # 45度旋转
        20: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]),  # 回到原位
        50: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])   # 结束帧
    }
    
    # 创建曲线数据
    curve_data = [0.0, 0.2, 0.5, 0.8, 1.0]
    reversed_curve_data = [1.0, 0.8, 0.5, 0.2, 0.0]
    
    # 测试1: 启用随机闭眼
    print("\n1. 启用随机闭眼（30%概率）:")
    analyzer1 = AnalysisEyeballRotation(
        frame_matrix_dict=frame_matrix_dict,
        curve_data=curve_data,
        reversed_curve_data=reversed_curve_data
    )
    analyzer1.frame_spacing = 2
    analyzer1.angle_threshold = 15
    analyzer1.set_random_blink_settings(
        enable=True,
        probability=0.3,
        min_interval=8,
        max_interval=15
    )
    
    result1 = analyzer1.analyze_rotation_changes()
    print(f"结果: {len(result1)} 个标记")
    for frame in sorted(result1.keys()):
        print(f"  帧 {frame}: {result1[frame]:.3f}")
    
    # 测试2: 高概率随机闭眼
    print("\n2. 高概率随机闭眼（80%概率）:")
    analyzer2 = AnalysisEyeballRotation(
        frame_matrix_dict=frame_matrix_dict,
        curve_data=curve_data,
        reversed_curve_data=reversed_curve_data
    )
    analyzer2.frame_spacing = 2
    analyzer2.angle_threshold = 15
    analyzer2.set_random_blink_settings(
        enable=True,
        probability=0.8,
        min_interval=5,
        max_interval=10
    )
    
    result2 = analyzer2.analyze_rotation_changes()
    print(f"结果: {len(result2)} 个标记")
    for frame in sorted(result2.keys()):
        print(f"  帧 {frame}: {result2[frame]:.3f}")
    
    # 测试3: 禁用随机闭眼
    print("\n3. 禁用随机闭眼:")
    analyzer3 = AnalysisEyeballRotation(
        frame_matrix_dict=frame_matrix_dict,
        curve_data=curve_data,
        reversed_curve_data=reversed_curve_data
    )
    analyzer3.frame_spacing = 2
    analyzer3.angle_threshold = 15
    analyzer3.set_random_blink_settings(enable=False)
    
    result3 = analyzer3.analyze_rotation_changes()
    print(f"结果: {len(result3)} 个标记")
    for frame in sorted(result3.keys()):
        print(f"  帧 {frame}: {result3[frame]:.3f}")
    
    # 比较结果
    print(f"\n比较结果:")
    print(f"  启用随机闭眼(30%): {len(result1)} 个标记")
    print(f"  高概率随机闭眼(80%): {len(result2)} 个标记")
    print(f"  禁用随机闭眼: {len(result3)} 个标记")
    
    return result1, result2, result3


def test_random_blink_conflict_avoidance():
    """
    测试随机闭眼的冲突避免功能
    """
    print("\n=== 测试随机闭眼冲突避免功能 ===")
    
    # 创建有多个眨眼的测试数据
    frame_matrix_dict = {}
    for i in range(1, 101):  # 100帧
        if i in [10, 30, 50, 70]:  # 在这些帧创建旋转变化
            frame_matrix_dict[i] = OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.5, -0.866, 0, 0, 0.866, 0.5, 0, 0, 0, 0, 1])
        else:
            frame_matrix_dict[i] = OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])
    
    # 创建分析器
    analyzer = AnalysisEyeballRotation(frame_matrix_dict=frame_matrix_dict)
    analyzer.frame_spacing = 2
    analyzer.angle_threshold = 15
    analyzer.set_random_blink_settings(
        enable=True,
        probability=1.0,  # 100%概率，确保生成随机闭眼
        min_interval=5,
        max_interval=8
    )
    
    result = analyzer.analyze_rotation_changes()
    
    print(f"总标记数: {len(result)}")
    print("标记分布:")
    
    # 分析标记分布
    original_frames = []
    random_frames = []
    
    # 简单的启发式方法来区分原始和随机标记
    # 原始标记通常在检测到旋转变化的帧附近
    detection_frames = [8, 28, 48, 68]  # 预期的检测帧（考虑偏移）
    
    for frame in sorted(result.keys()):
        is_original = False
        for det_frame in detection_frames:
            if abs(frame - det_frame) <= 10:  # 在检测帧±10帧范围内
                is_original = True
                break
        
        if is_original:
            original_frames.append(frame)
        else:
            random_frames.append(frame)
    
    print(f"  可能的原始标记: {len(original_frames)} 个 - 帧: {original_frames}")
    print(f"  可能的随机标记: {len(random_frames)} 个 - 帧: {random_frames}")
    
    # 检查冲突
    conflicts = 0
    for orig_frame in original_frames:
        for rand_frame in random_frames:
            if abs(orig_frame - rand_frame) <= 5:  # 缓冲区范围
                conflicts += 1
                print(f"  ⚠️  可能的冲突: 原始帧{orig_frame} 与 随机帧{rand_frame}")
    
    if conflicts == 0:
        print("  ✅ 没有检测到冲突")
    else:
        print(f"  ❌ 检测到 {conflicts} 个可能的冲突")
    
    return result


def test_random_blink_settings():
    """
    测试随机闭眼参数设置功能
    """
    print("\n=== 测试随机闭眼参数设置功能 ===")
    
    # 创建分析器
    analyzer = AnalysisEyeballRotation()
    
    # 测试默认设置
    print("\n1. 默认设置:")
    default_settings = analyzer.get_random_blink_settings()
    print(f"   {default_settings}")
    
    # 测试参数设置
    print("\n2. 自定义设置:")
    analyzer.set_random_blink_settings(
        enable=True,
        probability=0.5,
        min_interval=15,
        max_interval=25
    )
    custom_settings = analyzer.get_random_blink_settings()
    print(f"   {custom_settings}")
    
    # 测试边界值
    print("\n3. 边界值测试:")
    analyzer.set_random_blink_settings(
        enable=True,
        probability=1.5,  # 超过1.0，应该被限制为1.0
        min_interval=2,   # 小于5，应该被限制为5
        max_interval=3    # 小于min_interval，应该被调整
    )
    boundary_settings = analyzer.get_random_blink_settings()
    print(f"   {boundary_settings}")
    
    # 验证边界值处理
    assert boundary_settings['probability'] == 1.0, "概率应该被限制为1.0"
    assert boundary_settings['min_interval'] >= 5, "最小间隔应该不少于5帧"
    assert boundary_settings['max_interval'] > boundary_settings['min_interval'], "最大间隔应该大于最小间隔"
    
    print("   ✅ 边界值处理正确")
    
    return analyzer


def test_random_blink_with_different_curves():
    """
    测试随机闭眼与不同曲线数据的配合
    """
    print("\n=== 测试随机闭眼与不同曲线数据的配合 ===")
    
    # 创建测试矩阵数据
    frame_matrix_dict = {
        1: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]),
        50: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])
    }
    
    # 测试不同的曲线类型
    curve_types = {
        "线性": ([0.0, 0.25, 0.5, 0.75, 1.0], [1.0, 0.75, 0.5, 0.25, 0.0]),
        "缓入": ([0.0, 0.1, 0.3, 0.7, 1.0], [1.0, 0.9, 0.7, 0.3, 0.0]),
        "缓出": ([0.0, 0.3, 0.7, 0.9, 1.0], [1.0, 0.7, 0.3, 0.1, 0.0]),
        "无曲线": (None, None)
    }
    
    for curve_name, (curve_data, reversed_curve_data) in curve_types.items():
        print(f"\n测试 {curve_name} 曲线:")
        
        analyzer = AnalysisEyeballRotation(
            frame_matrix_dict=frame_matrix_dict,
            curve_data=curve_data,
            reversed_curve_data=reversed_curve_data
        )
        analyzer.frame_spacing = 2
        analyzer.angle_threshold = 15
        analyzer.set_random_blink_settings(
            enable=True,
            probability=1.0,  # 100%概率确保生成
            min_interval=10,
            max_interval=15
        )
        
        result = analyzer.analyze_rotation_changes()
        print(f"  结果: {len(result)} 个标记")
        
        # 显示前几个结果
        for i, (frame, value) in enumerate(sorted(result.items())[:6]):
            print(f"    帧 {frame}: {value:.3f}")
            if i >= 5:
                break


def run_all_tests():
    """
    运行所有随机闭眼测试
    """
    print("=== 随机闭眼功能测试 ===")
    
    try:
        # 基本生成功能测试
        test_random_blink_generation()
        
        # 冲突避免功能测试
        test_random_blink_conflict_avoidance()
        
        # 参数设置功能测试
        test_random_blink_settings()
        
        # 与不同曲线数据的配合测试
        test_random_blink_with_different_curves()
        
        print("\n=== 所有测试完成 ===")
        print("✅ 随机闭眼功能正常工作")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
