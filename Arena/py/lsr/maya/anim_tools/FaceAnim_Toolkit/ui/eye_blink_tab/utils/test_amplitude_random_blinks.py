# -*- coding: utf-8 -*-

"""
大幅度/小幅度随机眨眼功能测试脚本

用于测试analysis_eyeball_rotation.py中新增的大幅度和小幅度随机眨眼功能
"""

import math
from maya import OpenMaya
from .analysis_eyeball_rotation import AnalysisEyeballRotation


def test_amplitude_based_random_blinks():
    """
    测试基于幅度的随机眨眼功能
    """
    print("\n=== 测试基于幅度的随机眨眼功能 ===")
    
    # 创建测试矩阵数据
    frame_matrix_dict = {
        1: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]),
        50: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])
    }
    
    # 创建曲线数据
    curve_data = [0.0, 0.2, 0.5, 0.8, 1.0]
    reversed_curve_data = [1.0, 0.8, 0.5, 0.2, 0.0]
    
    # 测试不同的大幅度眨眼概率
    test_cases = [
        (0.0, "只有小幅度眨眼"),
        (0.3, "偏向小幅度眨眼"),
        (0.5, "平衡大小幅度眨眼"),
        (0.7, "偏向大幅度眨眼"),
        (1.0, "只有大幅度眨眼")
    ]
    
    for large_amplitude_prob, description in test_cases:
        print(f"\n--- {description} (大幅度概率: {large_amplitude_prob * 100:.0f}%) ---")
        
        analyzer = AnalysisEyeballRotation(
            frame_matrix_dict=frame_matrix_dict,
            curve_data=curve_data,
            reversed_curve_data=reversed_curve_data
        )
        analyzer.frame_spacing = 2
        analyzer.angle_threshold = 15
        analyzer.set_random_blink_settings(
            enable=True,
            probability=1.0,  # 100%概率确保生成随机眨眼
            min_interval=5,
            max_interval=8,
            large_amplitude_probability=large_amplitude_prob
        )
        
        result = analyzer.analyze_rotation_changes()
        
        print(f"生成的标记数: {len(result)}")
        if result:
            print("标记详情:")
            for frame in sorted(result.keys()):
                print(f"  帧 {frame}: {result[frame]:.3f}")


def test_random_blink_timing_patterns():
    """
    测试随机眨眼的时机模式
    """
    print("\n=== 测试随机眨眼时机模式 ===")
    
    # 创建测试矩阵数据
    frame_matrix_dict = {
        1: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]),
        100: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])
    }
    
    # 测试多次运行，观察时机模式的随机性
    print("\n运行多次测试，观察大幅度和小幅度眨眼的时机模式:")
    
    for run in range(5):
        print(f"\n第 {run + 1} 次运行:")
        
        analyzer = AnalysisEyeballRotation(frame_matrix_dict=frame_matrix_dict)
        analyzer.frame_spacing = 2
        analyzer.angle_threshold = 15
        analyzer.set_random_blink_settings(
            enable=True,
            probability=1.0,  # 100%概率
            min_interval=8,
            max_interval=12,
            large_amplitude_probability=0.5  # 50%概率
        )
        
        result = analyzer.analyze_rotation_changes()
        
        # 分析眨眼模式
        if result:
            frames = sorted(result.keys())
            print(f"  标记帧: {frames}")
            
            # 分析眨眼持续时间
            i = 0
            while i < len(frames):
                if result[frames[i]] == 0:  # 眨眼开始
                    start_frame = frames[i]
                    
                    # 找到对应的1和结束的0
                    peak_frame = None
                    end_frame = None
                    
                    for j in range(i + 1, len(frames)):
                        if result[frames[j]] == 1 and peak_frame is None:
                            peak_frame = frames[j]
                        elif result[frames[j]] == 0 and peak_frame is not None:
                            end_frame = frames[j]
                            break
                    
                    if peak_frame and end_frame:
                        duration = end_frame - start_frame
                        peak_offset = peak_frame - start_frame
                        
                        # 根据时机判断眨眼类型
                        if peak_offset <= 2 and duration <= 5:
                            blink_type = "小幅度"
                        elif peak_offset >= 2 and duration >= 7:
                            blink_type = "大幅度"
                        else:
                            blink_type = "未知"
                        
                        print(f"    眨眼: 帧{start_frame}-{peak_frame}-{end_frame}, 持续{duration}帧, 类型: {blink_type}")
                        
                        # 跳到这个眨眼序列的结束
                        i = frames.index(end_frame) + 1
                    else:
                        i += 1
                else:
                    i += 1


def test_curve_data_with_amplitude_blinks():
    """
    测试曲线数据与幅度眨眼的结合
    """
    print("\n=== 测试曲线数据与幅度眨眼的结合 ===")
    
    # 创建测试矩阵数据
    frame_matrix_dict = {
        1: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]),
        50: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])
    }
    
    # 创建不同类型的曲线数据
    curve_types = {
        "线性": ([0.0, 0.25, 0.5, 0.75, 1.0], [1.0, 0.75, 0.5, 0.25, 0.0]),
        "缓入": ([0.0, 0.1, 0.3, 0.7, 1.0], [1.0, 0.9, 0.7, 0.3, 0.0]),
        "缓出": ([0.0, 0.3, 0.7, 0.9, 1.0], [1.0, 0.7, 0.3, 0.1, 0.0])
    }
    
    for curve_name, (curve_data, reversed_curve_data) in curve_types.items():
        print(f"\n测试 {curve_name} 曲线:")
        
        # 测试偏向大幅度眨眼
        analyzer = AnalysisEyeballRotation(
            frame_matrix_dict=frame_matrix_dict,
            curve_data=curve_data,
            reversed_curve_data=reversed_curve_data
        )
        analyzer.frame_spacing = 2
        analyzer.angle_threshold = 15
        analyzer.set_random_blink_settings(
            enable=True,
            probability=1.0,
            min_interval=8,
            max_interval=12,
            large_amplitude_probability=0.8  # 80%概率为大幅度
        )
        
        result = analyzer.analyze_rotation_changes()
        
        print(f"  结果: {len(result)} 个标记")
        if result:
            # 显示前几个结果
            for i, (frame, value) in enumerate(sorted(result.items())[:8]):
                print(f"    帧 {frame}: {value:.3f}")
                if i >= 7:
                    break


def test_settings_validation():
    """
    测试设置参数的验证
    """
    print("\n=== 测试设置参数验证 ===")
    
    analyzer = AnalysisEyeballRotation()
    
    # 测试边界值
    test_cases = [
        {"large_amplitude_probability": -0.1, "expected": 0.0, "desc": "负值应被限制为0.0"},
        {"large_amplitude_probability": 1.5, "expected": 1.0, "desc": "超过1.0应被限制为1.0"},
        {"large_amplitude_probability": 0.5, "expected": 0.5, "desc": "正常值应保持不变"}
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['desc']}")
        analyzer.set_random_blink_settings(
            large_amplitude_probability=case["large_amplitude_probability"]
        )
        
        settings = analyzer.get_random_blink_settings()
        actual = settings['large_amplitude_probability']
        expected = case['expected']
        
        if abs(actual - expected) < 0.001:
            print(f"  ✅ 通过: 输入{case['large_amplitude_probability']} -> 输出{actual}")
        else:
            print(f"  ❌ 失败: 输入{case['large_amplitude_probability']} -> 期望{expected}, 实际{actual}")


def run_all_amplitude_tests():
    """
    运行所有幅度相关的测试
    """
    print("=== 大幅度/小幅度随机眨眼功能测试 ===")
    
    try:
        # 基于幅度的随机眨眼测试
        test_amplitude_based_random_blinks()
        
        # 时机模式测试
        test_random_blink_timing_patterns()
        
        # 曲线数据结合测试
        test_curve_data_with_amplitude_blinks()
        
        # 设置验证测试
        test_settings_validation()
        
        print("\n=== 所有测试完成 ===")
        print("✅ 大幅度/小幅度随机眨眼功能正常工作")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_amplitude_tests()
