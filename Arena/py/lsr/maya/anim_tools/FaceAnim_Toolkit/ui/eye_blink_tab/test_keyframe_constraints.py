#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试关键帧移动限制逻辑（不需要GUI）
"""

import sys
import math
import copy


class BezierPoint:
    """贝塞尔曲线关键点"""
    
    def __init__(self, time=0.0, value=0.0, in_tangent=(0, 0), out_tangent=(0, 0)):
        self.time = time
        self.value = value
        self.in_tangent = in_tangent
        self.out_tangent = out_tangent
        self.selected = False


class MockBezierCurveWidget:
    """模拟贝塞尔曲线编辑器控件（仅用于测试逻辑）"""
    
    def __init__(self):
        # 曲线数据
        self.keyframes = []
        self.selected_keyframe = None
        
        # 视图参数
        self.time_range = (0.0, 10.0)
        self.value_range = (-1.0, 1.0)
        
        # 初始化默认关键帧（位置固定）
        self.add_keyframe(self.time_range[0], 0.0)  # 最左边
        mid_time = (self.time_range[0] + self.time_range[1]) / 2
        self.add_keyframe(mid_time, 1.0)  # 中间
        self.add_keyframe(self.time_range[1], 0.0)  # 最右边
    
    def add_keyframe(self, time, value):
        """添加关键帧"""
        in_tangent = (-1.0, 0.0)
        out_tangent = (1.0, 0.0)
        
        keyframe = BezierPoint(time, value, in_tangent, out_tangent)
        self.keyframes.append(keyframe)
        self.keyframes.sort(key=lambda k: k.time)
        return keyframe
    
    def get_keyframe_index(self, keyframe):
        """获取关键帧在列表中的索引"""
        if keyframe and keyframe in self.keyframes:
            return self.keyframes.index(keyframe)
        return -1
    
    def simulate_drag_keyframe(self, keyframe_index, new_time, new_value):
        """模拟拖拽关键帧"""
        if keyframe_index < 0 or keyframe_index >= len(self.keyframes):
            return False
        
        dragging_keyframe = self.keyframes[keyframe_index]
        
        print(f"\n=== 模拟拖拽关键帧 {keyframe_index + 1} ===")
        print(f"原始位置: 时间={dragging_keyframe.time:.3f}, 值={dragging_keyframe.value:.3f}")
        print(f"目标位置: 时间={new_time:.3f}, 值={new_value:.3f}")
        
        # 应用移动限制逻辑
        if keyframe_index == 0:
            # 第一个关键帧：只能在最左边上下移动
            dragging_keyframe.time = self.time_range[0]
            dragging_keyframe.value = max(self.value_range[0], min(self.value_range[1], new_value))
            print(f"限制结果: 时间固定在最左边 {self.time_range[0]:.3f}, 值={dragging_keyframe.value:.3f}")
        elif keyframe_index == 1:
            # 第二个关键帧：只能在中间移动（时间固定在中点）
            mid_time = (self.time_range[0] + self.time_range[1]) / 2
            dragging_keyframe.time = mid_time
            dragging_keyframe.value = max(self.value_range[0], min(self.value_range[1], new_value))
            print(f"限制结果: 时间固定在中间 {mid_time:.3f}, 值={dragging_keyframe.value:.3f}")
        elif keyframe_index == 2:
            # 第三个关键帧：只能在最右边上下移动
            dragging_keyframe.time = self.time_range[1]
            dragging_keyframe.value = max(self.value_range[0], min(self.value_range[1], new_value))
            print(f"限制结果: 时间固定在最右边 {self.time_range[1]:.3f}, 值={dragging_keyframe.value:.3f}")
        else:
            # 其他关键帧：正常移动
            dragging_keyframe.time = max(self.time_range[0], min(self.time_range[1], new_time))
            dragging_keyframe.value = max(self.value_range[0], min(self.value_range[1], new_value))
            print(f"限制结果: 时间={dragging_keyframe.time:.3f}, 值={dragging_keyframe.value:.3f}")
        
        self.keyframes.sort(key=lambda k: k.time)
        return True
    
    def simulate_time_change(self, keyframe_index, new_time):
        """模拟通过属性面板修改时间"""
        if keyframe_index < 0 or keyframe_index >= len(self.keyframes):
            return False
        
        current_keyframe = self.keyframes[keyframe_index]
        old_time = current_keyframe.time
        
        print(f"\n=== 模拟修改关键帧 {keyframe_index + 1} 时间 ===")
        print(f"原始时间: {old_time:.3f}")
        print(f"目标时间: {new_time:.3f}")
        
        # 根据关键帧索引限制时间修改
        if keyframe_index == 0:
            # 第一个关键帧：时间固定在最左边
            new_time = self.time_range[0]
            print(f"第一个关键帧时间固定在最左边 {new_time:.3f}，无法修改")
        elif keyframe_index == 1:
            # 第二个关键帧：时间固定在中间
            mid_time = (self.time_range[0] + self.time_range[1]) / 2
            new_time = mid_time
            print(f"第二个关键帧时间固定在中间 {new_time:.3f}，无法修改")
        elif keyframe_index == 2:
            # 第三个关键帧：时间固定在最右边
            new_time = self.time_range[1]
            print(f"第三个关键帧时间固定在最右边 {new_time:.3f}，无法修改")
        else:
            print(f"其他关键帧可以自由修改时间: {new_time:.3f}")
        
        current_keyframe.time = new_time
        self.keyframes.sort(key=lambda k: k.time)
        return True
    
    def update_fixed_keyframe_positions(self):
        """更新固定关键帧的位置"""
        if len(self.keyframes) >= 3:
            print(f"\n=== 更新固定关键帧位置 ===")
            print(f"时间范围: {self.time_range[0]:.3f} 到 {self.time_range[1]:.3f}")
            
            # 更新第一个关键帧位置（最左边）
            old_time_1 = self.keyframes[0].time
            self.keyframes[0].time = self.time_range[0]
            print(f"第一个关键帧: {old_time_1:.3f} -> {self.keyframes[0].time:.3f}")
            
            # 更新第二个关键帧位置（中间）
            mid_time = (self.time_range[0] + self.time_range[1]) / 2
            old_time_2 = self.keyframes[1].time
            self.keyframes[1].time = mid_time
            print(f"第二个关键帧: {old_time_2:.3f} -> {self.keyframes[1].time:.3f}")
            
            # 更新第三个关键帧位置（最右边）
            old_time_3 = self.keyframes[2].time
            self.keyframes[2].time = self.time_range[1]
            print(f"第三个关键帧: {old_time_3:.3f} -> {self.keyframes[2].time:.3f}")
            
            # 重新排序关键帧
            self.keyframes.sort(key=lambda k: k.time)
    
    def print_keyframes(self):
        """打印关键帧信息"""
        print(f"\n=== 当前关键帧状态 ===")
        print(f"时间范围: {self.time_range[0]:.3f} 到 {self.time_range[1]:.3f}")
        for i, kf in enumerate(self.keyframes):
            position_desc = ""
            if i == 0:
                position_desc = "（最左边）"
            elif i == 1:
                position_desc = "（中间）"
            elif i == 2:
                position_desc = "（最右边）"
            print(f"关键帧 {i+1}: 时间={kf.time:.3f}, 值={kf.value:.3f} {position_desc}")


def test_keyframe_constraints():
    """测试关键帧移动限制"""
    print("=== 贝塞尔曲线关键帧移动限制测试 ===")
    
    # 创建模拟编辑器
    editor = MockBezierCurveWidget()
    editor.print_keyframes()
    
    # 测试1: 尝试拖拽第一个关键帧
    print("\n" + "="*50)
    print("测试1: 尝试拖拽第一个关键帧到中间")
    editor.simulate_drag_keyframe(0, 5.0, 0.5)  # 尝试移动到中间
    editor.print_keyframes()
    
    # 测试2: 尝试拖拽第二个关键帧
    print("\n" + "="*50)
    print("测试2: 尝试拖拽第二个关键帧到右边")
    editor.simulate_drag_keyframe(1, 8.0, 0.8)  # 尝试移动到右边
    editor.print_keyframes()
    
    # 测试3: 尝试拖拽第三个关键帧
    print("\n" + "="*50)
    print("测试3: 尝试拖拽第三个关键帧到左边")
    editor.simulate_drag_keyframe(2, 2.0, -0.5)  # 尝试移动到左边
    editor.print_keyframes()
    
    # 测试4: 尝试通过属性面板修改时间
    print("\n" + "="*50)
    print("测试4: 尝试通过属性面板修改关键帧时间")
    editor.simulate_time_change(0, 3.0)  # 尝试修改第一个关键帧时间
    editor.simulate_time_change(1, 7.0)  # 尝试修改第二个关键帧时间
    editor.simulate_time_change(2, 1.0)  # 尝试修改第三个关键帧时间
    editor.print_keyframes()
    
    # 测试5: 修改时间范围
    print("\n" + "="*50)
    print("测试5: 修改时间范围")
    editor.time_range = (-5.0, 15.0)
    editor.update_fixed_keyframe_positions()
    editor.print_keyframes()
    
    print("\n" + "="*50)
    print("✅ 所有测试完成！")
    print("关键帧移动限制功能正常工作：")
    print("- 第一个关键帧只能在最左边上下移动")
    print("- 第二个关键帧只能在中间上下移动") 
    print("- 第三个关键帧只能在最右边上下移动")
    print("- 时间范围改变时，关键帧位置自动调整")


if __name__ == '__main__':
    test_keyframe_constraints()
