# -*- coding: utf-8 -*-

"""
测试额外1帧提前的逻辑
"""

import random

def test_extra_advance_timing():
    """
    测试额外1帧提前的眨眼时机：
    - 原来偏移：frame_spacing
    - 现在偏移：frame_spacing + 1
    """
    print("=== 测试额外1帧提前的眨眼时机逻辑 ===")
    
    # 参数设置
    frame_spacing = 2
    angle_threshold = 15
    amplitude_threshold = 1.5 * angle_threshold  # 22.5度
    cooldown_frames = 2 * frame_spacing
    
    print(f"参数设置:")
    print(f"- 帧间隔: {frame_spacing}")
    print(f"- 角度阈值: {angle_threshold}度")
    print(f"- 幅度阈值: {amplitude_threshold}度")
    print(f"- 冷却期: {cooldown_frames}帧")
    print(f"- 总偏移: {frame_spacing + 1}帧（frame_spacing + 1）")
    
    # 测试数据
    rotation_data = {
        1: 5,   # 未超过阈值
        2: 8,   # 未超过阈值
        3: 20,  # 超过阈值，眨眼开始
        4: 18,  # 眨眼进行中
        5: 15,  # 可能输出1
        6: 12,  # 可能输出1或0
        7: 10,  # 可能输出0
        8: 8,   # 可能输出0
        9: 6,   # 等待期
        10: 5,  # 等待期
        11: 4,  # 等待期
        12: 3,  # 等待期结束
    }
    
    def simulate_extra_advance(rotation_data, test_runs=3):
        print(f"\n--- 额外1帧提前测试 ---")
        
        results = []
        
        for run in range(test_runs):
            print(f"\n运行 {run + 1}:")
            
            result_markers = {}
            last_end_frame = None
            pending_blinks = {}
            
            for current_frame in sorted(rotation_data.keys()):
                rotation_diff = rotation_data[current_frame]
                
                # 处理待处理的眨眼标记
                blinks_to_remove = []
                for start_frame, blink_data in pending_blinks.items():
                    amplitude, end_offset, complete_offset = blink_data
                    frame_offset = current_frame - start_frame
                    
                    if amplitude < amplitude_threshold:
                        # 小幅度眨眼：随机时机（考虑偏移+额外1帧）
                        if frame_offset == end_offset:
                            output_frame = current_frame - frame_spacing - 1
                            result_markers[output_frame] = 1
                            print(f"  帧 {current_frame}: 小幅度眨眼结束，在帧{output_frame}标记1（偏移+1帧，随机第{end_offset}帧）")
                        elif frame_offset == complete_offset:
                            output_frame = current_frame - frame_spacing - 1
                            result_markers[output_frame] = 0
                            last_end_frame = current_frame
                            print(f"  帧 {current_frame}: 小幅度眨眼周期完成，在帧{output_frame}标记0（偏移+1帧，随机第{complete_offset}帧）")
                            blinks_to_remove.append(start_frame)
                    else:
                        # 大幅度眨眼：随机时机（考虑偏移+额外1帧）
                        if frame_offset == end_offset:
                            output_frame = current_frame - frame_spacing - 1
                            result_markers[output_frame] = 1
                            print(f"  帧 {current_frame}: 大幅度眨眼结束，在帧{output_frame}标记1（偏移+1帧，随机第{end_offset}帧）")
                        elif frame_offset == complete_offset:
                            output_frame = current_frame - frame_spacing - 1
                            result_markers[output_frame] = 0
                            last_end_frame = current_frame
                            print(f"  帧 {current_frame}: 大幅度眨眼周期完成，在帧{output_frame}标记0（偏移+1帧，随机第{complete_offset}帧）")
                            blinks_to_remove.append(start_frame)
                
                # 移除已处理完的眨眼
                for start_frame in blinks_to_remove:
                    del pending_blinks[start_frame]
                
                # 检查是否在冷却期内或有待处理的眨眼
                if (last_end_frame is not None and current_frame - last_end_frame < cooldown_frames) or pending_blinks:
                    continue
                
                # 判断当前是否超过阈值
                current_exceeds = rotation_diff > angle_threshold
                
                if current_exceeds:
                    # 检测到眨眼开始（考虑偏移+额外1帧）
                    blink_start_frame = current_frame - frame_spacing - 1
                    result_markers[blink_start_frame] = 0
                    
                    # 模拟分析眨眼幅度
                    max_amplitude = max([rotation_data.get(current_frame + i, 0) 
                                       for i in range(min(10, len(rotation_data) - current_frame + 1))])
                    
                    # 生成随机时机
                    if max_amplitude < amplitude_threshold:
                        # 小幅度眨眼：随机第1或2帧输出1，然后随机+2或3帧输出0
                        end_offset = random.choice([1, 2])
                        complete_offset = end_offset + random.choice([2, 3])
                        blink_type = "小幅度"
                    else:
                        # 大幅度眨眼：随机第2或3帧输出1，然后随机+5或6帧输出0
                        end_offset = random.choice([2, 3])
                        complete_offset = end_offset + random.choice([5, 6])
                        blink_type = "大幅度"
                    
                    pending_blinks[current_frame] = (max_amplitude, end_offset, complete_offset)
                    
                    print(f"  帧 {current_frame}: 眨眼开始! 在帧{blink_start_frame}标记0（偏移+1帧）")
                    print(f"       幅度: {max_amplitude:.1f}度 ({blink_type}眨眼)")
                    print(f"       随机时机: 第{end_offset}帧输出1，第{complete_offset}帧输出0")
            
            results.append(result_markers)
            print(f"  结果: {result_markers}")
        
        return results
    
    # 执行测试
    results = simulate_extra_advance(rotation_data, 3)
    
    print(f"\n=== 偏移对比分析 ===")
    print(f"原来偏移（frame_spacing = {frame_spacing}）:")
    print(f"- 如果帧3检测到眨眼，标记会在帧{3 - frame_spacing} = 帧1")
    print(f"- 如果帧4输出1，标记会在帧{4 - frame_spacing} = 帧2")
    
    print(f"\n现在偏移（frame_spacing + 1 = {frame_spacing + 1}）:")
    print(f"- 如果帧3检测到眨眼，标记会在帧{3 - frame_spacing - 1} = 帧0")
    print(f"- 如果帧4输出1，标记会在帧{4 - frame_spacing - 1} = 帧1")
    
    print(f"\n多次运行结果:")
    for i, result in enumerate(results):
        print(f"  运行{i+1}: {result}")
    
    # 分析偏移效果
    if results:
        first_result = results[0]
        start_frames = [f for f, v in first_result.items() if v == 0]
        end_frames = [f for f, v in first_result.items() if v == 1]
        
        if start_frames and end_frames:
            print(f"\n偏移效果分析（以第一次运行为例）:")
            print(f"- 眨眼开始标记: 帧{start_frames[0]} (比原来提前了1帧)")
            print(f"- 眨眼结束标记: 帧{end_frames[0]} (比原来提前了1帧)")
            print(f"✅ 额外1帧提前生效！")
        else:
            print("⚠️  未检测到完整的眨眼周期")
    else:
        print("❌ 测试失败，未产生结果")


if __name__ == "__main__":
    test_extra_advance_timing()
