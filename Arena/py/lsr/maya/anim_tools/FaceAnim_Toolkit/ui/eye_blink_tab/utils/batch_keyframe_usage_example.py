"""
批量关键帧创建使用示例

这个文件展示了如何使用优化后的批量关键帧创建函数，
避免时间轴重复运行的问题。
"""

import maya.cmds as cmds
from analysis_eyeball_rotation import (
    create_keyframes_from_analysis,
    create_keyframes_from_analysis_batch,
    analyze_eyeball_rotation_with_curve
)


def example_single_object():
    """单个对象的关键帧创建示例（原始方法）"""
    print("=== 单个对象关键帧创建示例 ===")
    
    # 模拟分析结果
    analysis_result = {
        10: 0,  # 眨眼开始
        15: 1,  # 眨眼结束
        20: 0,  # 眨眼周期完成
        30: 0,  # 下一次眨眼开始
        35: 1,  # 眨眼结束
        40: 0   # 眨眼周期完成
    }
    
    # 单个对象
    object_name = "CTRL_L_eye_blink"
    create_keyframes_from_analysis(object_name, analysis_result, "translateY")


def example_multiple_objects_old_way():
    """多个对象的关键帧创建示例（旧方法 - 时间轴会运行多次）"""
    print("=== 多个对象关键帧创建示例（旧方法 - 效率低）===")
    
    # 模拟分析结果
    analysis_result = {
        10: 0,  # 眨眼开始
        15: 1,  # 眨眼结束
        20: 0,  # 眨眼周期完成
        30: 0,  # 下一次眨眼开始
        35: 1,  # 眨眼结束
        40: 0   # 眨眼周期完成
    }
    
    # 多个对象（旧方法）
    eye_blink_ctrls = ["CTRL_L_eye_blink", "CTRL_R_eye_blink"]
    
    print("⚠️ 注意：这种方法会让时间轴运行多次，效率较低")
    for eye_blink_ctrl in eye_blink_ctrls:
        create_keyframes_from_analysis(eye_blink_ctrl, analysis_result, "translateY")


def example_multiple_objects_optimized():
    """多个对象的关键帧创建示例（优化方法 - 时间轴只运行一次）"""
    print("=== 多个对象关键帧创建示例（优化方法 - 高效）===")
    
    # 模拟分析结果
    analysis_result = {
        10: 0,  # 眨眼开始
        15: 1,  # 眨眼结束
        20: 0,  # 眨眼周期完成
        30: 0,  # 下一次眨眼开始
        35: 1,  # 眨眼结束
        40: 0   # 眨眼周期完成
    }
    
    # 多个对象（优化方法）
    eye_blink_ctrls = ["CTRL_L_eye_blink", "CTRL_R_eye_blink"]
    
    print("✅ 优化方法：时间轴只运行一次，效率更高")
    create_keyframes_from_analysis_batch(eye_blink_ctrls, analysis_result, "translateY")


def example_with_namespace():
    """带命名空间的多个对象示例"""
    print("=== 带命名空间的多个对象示例 ===")
    
    # 模拟分析结果
    analysis_result = {
        10: 0,  # 眨眼开始
        15: 1,  # 眨眼结束
        20: 0,  # 眨眼周期完成
    }
    
    # 带命名空间的对象
    full_name_space = "character_rig"
    eye_blink_ctrls = [
        f"{full_name_space}:CTRL_L_eye_blink", 
        f"{full_name_space}:CTRL_R_eye_blink"
    ]
    
    # 使用批量函数
    create_keyframes_from_analysis_batch(eye_blink_ctrls, analysis_result, "translateY")


def example_different_attributes():
    """不同属性的批量关键帧创建示例"""
    print("=== 不同属性的批量关键帧创建示例 ===")
    
    # 模拟分析结果
    analysis_result = {
        10: 0,  # 眨眼开始
        15: 1,  # 眨眼结束
        20: 0,  # 眨眼周期完成
    }
    
    # 不同的对象和属性组合
    scenarios = [
        {
            'objects': ["CTRL_L_eye_blink", "CTRL_R_eye_blink"],
            'attribute': "translateY",
            'description': "眼皮控制器的Y轴移动"
        },
        {
            'objects': ["L_eyelid_upper", "R_eyelid_upper"],
            'attribute': "scaleY",
            'description': "上眼皮的Y轴缩放"
        },
        {
            'objects': ["L_eye_visibility", "R_eye_visibility"],
            'attribute': "visibility",
            'description': "眼部可见性控制"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n处理: {scenario['description']}")
        create_keyframes_from_analysis_batch(
            scenario['objects'], 
            analysis_result, 
            scenario['attribute']
        )


def example_complete_workflow():
    """完整工作流程示例"""
    print("=== 完整工作流程示例 ===")
    
    # 1. 获取选中的对象
    selected_objects = cmds.ls(selection=True)
    if not selected_objects:
        print("请先选择一个眼部对象")
        return
    
    object_name = selected_objects[0]
    print(f"分析对象: {object_name}")
    
    # 2. 执行眼动分析
    try:
        result = analyze_eyeball_rotation_with_curve(
            object_name=object_name,
            start_frame=1,
            end_frame=100,
            frame_spacing=2,
            angle_threshold=10
        )
        
        if not result:
            print("未检测到眨眼动作")
            return
            
        print(f"检测到 {len(result)} 个眨眼标记")
        
        # 3. 批量应用到多个控制器
        full_name_space = object_name.split(':')[0] if ':' in object_name else ""
        
        if full_name_space:
            eye_blink_ctrls = [
                f"{full_name_space}:CTRL_L_eye_blink", 
                f"{full_name_space}:CTRL_R_eye_blink"
            ]
        else:
            eye_blink_ctrls = ["CTRL_L_eye_blink", "CTRL_R_eye_blink"]
        
        # 使用优化的批量函数
        create_keyframes_from_analysis_batch(eye_blink_ctrls, result, "translateY")
        
        print("✅ 批量关键帧创建完成")
        
    except Exception as e:
        print(f"❌ 工作流程执行失败: {e}")


def performance_comparison():
    """性能对比示例"""
    print("=== 性能对比示例 ===")
    
    import time
    
    # 模拟大量关键帧数据
    analysis_result = {}
    for i in range(1, 101, 3):  # 每3帧一个标记，共33个标记
        analysis_result[i] = 0 if i % 6 == 1 else 1
    
    objects = ["CTRL_L_eye_blink", "CTRL_R_eye_blink", "CTRL_L_eye_upper", "CTRL_R_eye_upper"]
    
    print(f"测试数据: {len(analysis_result)} 个关键帧标记，{len(objects)} 个对象")
    
    # 方法1：逐个处理（旧方法）
    print("\n方法1：逐个处理（旧方法）")
    start_time = time.time()
    for obj in objects:
        create_keyframes_from_analysis(obj, analysis_result, "translateY")
    old_method_time = time.time() - start_time
    print(f"耗时: {old_method_time:.2f} 秒")
    
    # 清理关键帧
    for obj in objects:
        try:
            cmds.cutKey(obj, attribute="translateY")
        except:
            pass
    
    # 方法2：批量处理（新方法）
    print("\n方法2：批量处理（新方法）")
    start_time = time.time()
    create_keyframes_from_analysis_batch(objects, analysis_result, "translateY")
    new_method_time = time.time() - start_time
    print(f"耗时: {new_method_time:.2f} 秒")
    
    # 性能提升计算
    if new_method_time > 0:
        improvement = (old_method_time - new_method_time) / new_method_time * 100
        print(f"\n性能提升: {improvement:.1f}%")
        print(f"速度提升: {old_method_time / new_method_time:.1f}x")


if __name__ == "__main__":
    print("批量关键帧创建使用示例")
    print("=" * 50)
    
    # 运行示例
    example_single_object()
    print("\n" + "=" * 50)
    
    example_multiple_objects_old_way()
    print("\n" + "=" * 50)
    
    example_multiple_objects_optimized()
    print("\n" + "=" * 50)
    
    example_with_namespace()
    print("\n" + "=" * 50)
    
    example_different_attributes()
    print("\n" + "=" * 50)
    
    # 如果在Maya中运行，可以执行完整工作流程
    try:
        import maya.cmds as cmds
        example_complete_workflow()
        print("\n" + "=" * 50)
        
        # 性能对比需要谨慎运行，因为会创建大量关键帧
        # performance_comparison()
        
    except ImportError:
        print("不在Maya环境中，跳过Maya相关示例")
