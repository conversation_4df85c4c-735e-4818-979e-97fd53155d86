# 贝塞尔曲线关键帧移动限制功能

## 功能概述

为贝塞尔曲线编辑器添加了关键帧移动限制功能，确保默认的3个关键帧只能在指定位置进行有限的移动。

## 功能特性

### 关键帧移动限制

1. **第一个关键帧（索引0）**：
   - 时间固定在最左边（`time_range[0]`）
   - 只能上下移动（修改值）
   - 无法通过拖拽或属性面板修改时间

2. **第二个关键帧（索引1）**：
   - 时间固定在中间（`(time_range[0] + time_range[1]) / 2`）
   - 只能上下移动（修改值）
   - 无法通过拖拽或属性面板修改时间

3. **第三个关键帧（索引2）**：
   - 时间固定在最右边（`time_range[1]`）
   - 只能上下移动（修改值）
   - 无法通过拖拽或属性面板修改时间

### 自动位置调整

- 当时间范围改变时，关键帧位置自动调整
- 重置曲线时，关键帧位置根据当前时间范围正确设置

## 修改的文件和方法

### `BezierCurveWidget` 类修改

1. **`__init__` 方法**：
   - 修改初始关键帧创建逻辑，使用时间范围计算位置

2. **`mouseMoveEvent` 方法**：
   - 添加关键帧索引检查
   - 根据索引应用不同的移动限制

3. **新增 `update_fixed_keyframe_positions` 方法**：
   - 在时间范围改变时自动调整关键帧位置

### `KeyframePropertiesWidget` 类修改

1. **`on_time_changed` 方法**：
   - 添加关键帧索引检查
   - 阻止固定关键帧的时间修改
   - 提供用户反馈信息

### `BezierCurveEditor` 类修改

1. **`update_time_range` 方法**：
   - 调用 `update_fixed_keyframe_positions` 自动调整关键帧

2. **`reset_curve` 方法**：
   - 使用当前时间范围设置关键帧位置

## 测试验证

### 测试用例

1. **拖拽测试**：
   - ✅ 第一个关键帧拖拽时时间保持在最左边
   - ✅ 第二个关键帧拖拽时时间保持在中间
   - ✅ 第三个关键帧拖拽时时间保持在最右边
   - ✅ 所有关键帧都可以自由上下移动

2. **属性面板测试**：
   - ✅ 尝试修改固定关键帧时间时被阻止
   - ✅ 提供清晰的用户反馈信息
   - ✅ 值的修改不受限制

3. **时间范围测试**：
   - ✅ 时间范围改变时关键帧自动调整位置
   - ✅ 关键帧相对位置保持正确

4. **重置测试**：
   - ✅ 重置曲线后关键帧位置正确

### 测试结果示例

```
=== 测试1: 拖拽第一个关键帧 ===
原始位置: 时间=0.000, 值=0.000
目标位置: 时间=5.000, 值=0.500
限制结果: 时间固定在最左边 0.000, 值=0.500

=== 测试2: 修改时间范围 ===
时间范围: -5.000 到 15.000
第一个关键帧: 0.000 -> -5.000
第二个关键帧: 5.000 -> 5.000  
第三个关键帧: 10.000 -> 15.000
```

## 使用说明

### 基本操作

1. **移动关键帧**：
   - 拖拽关键帧只能上下移动
   - 时间位置自动保持在固定位置

2. **修改属性**：
   - 可以通过属性面板修改关键帧的值
   - 时间修改会被自动阻止并提示用户

3. **调整时间范围**：
   - 修改时间范围时，关键帧会自动调整到正确位置
   - 第一个关键帧始终在最左边
   - 第二个关键帧始终在中间
   - 第三个关键帧始终在最右边

### 注意事项

- 此限制仅适用于前3个关键帧（索引0、1、2）
- 如果添加更多关键帧，它们可以自由移动
- 删除关键帧时需要保持至少2个关键帧的限制仍然有效

## 兼容性

- 支持 Qt.py、PyQt5、PySide2
- 向后兼容现有的贝塞尔曲线编辑器功能
- 不影响切线编辑和其他现有功能

## 文件结构

```
eye_blink_tab/
├── bezier_curve_editor.py          # 主要修改的文件
├── test_keyframe_constraints.py    # 逻辑测试文件
├── test_bezier_constraints.py      # GUI测试文件
└── KEYFRAME_CONSTRAINTS_README.md  # 本文档
```
