# 曲线数据与眨眼分析集成

## 概述

这个更新为 `analysis_eyeball_rotation.py` 添加了曲线数据支持，允许在眨眼动画的0-1和1-0过渡过程中使用平滑的曲线数据，而不是简单的离散0,1,0值。

## 主要功能

### 1. 曲线数据支持
- **0-1过渡曲线**: 用于眨眼开始到闭眼的过程
- **1-0过渡曲线**: 用于闭眼到睁眼的过程
- **向后兼容**: 如果没有提供曲线数据，仍然使用原来的离散值方式

### 2. 新增参数
- `curve_data`: 0-1过渡的曲线值列表 `[value1, value2, ...]`
- `reversed_curve_data`: 1-0过渡的曲线值列表 `[value1, value2, ...]`

### 3. 随机闭眼机制
- **随机生成**: 30%概率生成规则外的闭眼动作
- **智能避让**: 自动避免与现有眨眼检测结果冲突
- **可配置参数**: 概率、间隔范围等都可以调整
- **曲线支持**: 随机闭眼也支持使用曲线数据生成平滑过渡

### 3. 自动过渡帧生成
- 在检测到眨眼时，自动在关键帧之间插入曲线过渡帧
- 根据帧数自动调整曲线采样

## 使用方法

### 方法1: 直接提供曲线数据

```python
from analysis_eyeball_rotation import AnalysisEyeballRotation

# 创建曲线数据
curve_data = [0.0, 0.2, 0.5, 0.8, 1.0]  # 0-1过渡
reversed_curve_data = [1.0, 0.7, 0.4, 0.1, 0.0]  # 1-0过渡

# 创建分析器
analyzer = AnalysisEyeballRotation(
    frame_matrix_dict=your_matrix_data,
    curve_data=curve_data,
    reversed_curve_data=reversed_curve_data
)

# 执行分析
result = analyzer.analyze_rotation_changes()
```

### 方法2: 使用贝塞尔曲线编辑器

```python
from analysis_eyeball_rotation import analyze_eyeball_with_curve_data
from bezier_curve_editor import BezierCurveWidget

# 创建曲线编辑器
curve_widget = BezierCurveWidget()
# ... 设置关键帧 ...

# 分析眼球旋转（自动从曲线编辑器提取数据）
result = analyze_eyeball_with_curve_data(
    object_name="eyeball_joint",
    curve_widget=curve_widget,
    frame_spacing=2,
    angle_threshold=15
)
```

### 方法3: 手动曲线数据

```python
# 创建自定义曲线（例如：缓入缓出效果）
curve_data = []
for i in range(20):
    t = i / 19.0
    value = 0.5 * (1 - math.cos(math.pi * t))  # 正弦缓动
    curve_data.append(value)

# 使用自定义曲线
result = analyze_eyeball_with_curve_data(
    object_name="eyeball_joint",
    curve_data=curve_data,
    reversed_curve_data=[1.0 - v for v in curve_data]  # 反转
)
```

### 方法4: 使用随机闭眼功能

```python
# 启用随机闭眼（默认30%概率）
result = analyze_eyeball_with_curve_data(
    object_name="eyeball_joint",
    curve_data=curve_data,
    reversed_curve_data=reversed_curve_data,
    enable_random_blinks=True,  # 启用随机闭眼
    random_blink_probability=0.3,  # 30%概率
    random_blink_min_interval=10,  # 最小间隔10帧
    random_blink_max_interval=30   # 最大间隔30帧
)

# 或者手动设置随机闭眼参数
analyzer = AnalysisEyeballRotation(frame_matrix_dict=matrix_data)
analyzer.set_random_blink_settings(
    enable=True,
    probability=0.5,  # 50%概率
    min_interval=15,
    max_interval=25
)
result = analyzer.analyze_rotation_changes()
```

## 输出格式

### 有曲线数据时
```python
{
    10: 0.000,    # 眨眼开始
    11: 0.200,    # 过渡帧1
    12: 0.500,    # 过渡帧2
    13: 0.800,    # 过渡帧3
    14: 1.000,    # 眨眼结束
    15: 0.700,    # 反转过渡帧1
    16: 0.400,    # 反转过渡帧2
    17: 0.100,    # 反转过渡帧3
    18: 0.000     # 眨眼周期完成
}
```

### 无曲线数据时（原来的方式）
```python
{
    10: 0,    # 眨眼开始
    14: 1,    # 眨眼结束
    18: 0     # 眨眼周期完成
}
```

### 包含随机闭眼时
```python
{
    10: 0.000,    # 检测到的眨眼开始
    11: 0.200,    # 过渡帧
    12: 0.500,    # 过渡帧
    13: 0.800,    # 过渡帧
    14: 1.000,    # 检测到的眨眼结束
    15: 0.700,    # 反转过渡帧
    16: 0.400,    # 反转过渡帧
    17: 0.100,    # 反转过渡帧
    18: 0.000,    # 检测到的眨眼周期完成

    # 随机生成的闭眼（与检测到的眨眼不冲突）
    25: 0.000,    # 随机闭眼开始
    26: 0.300,    # 随机闭眼过渡
    27: 0.700,    # 随机闭眼过渡
    28: 1.000,    # 随机闭眼结束
    29: 0.600,    # 随机闭眼反转过渡
    30: 0.200,    # 随机闭眼反转过渡
    31: 0.000     # 随机闭眼完成
}
```

## 技术细节

### 过渡帧生成算法
1. 检测到眨眼开始时，计算0-1过渡的结束帧
2. 根据帧数和曲线数据点数，计算每帧对应的曲线值
3. 在眨眼结束时，如果有反转曲线数据，生成1-0过渡帧

### 曲线数据要求
- 数据格式：数值列表 `[float, float, ...]`
- 0-1过渡曲线：建议从0.0开始，到1.0结束
- 1-0过渡曲线：建议从1.0开始，到0.0结束
- 采样点数：建议20-100个点，根据动画精度需求调整

### 随机闭眼算法
1. **冲突检测**: 为每个现有眨眼标记创建±5帧的缓冲区
2. **间隔生成**: 在指定的最小-最大间隔范围内随机选择检查点
3. **概率判断**: 在每个检查点按设定概率决定是否生成随机闭眼
4. **序列生成**: 生成3-5帧的完整闭眼序列（开始-峰值-结束）
5. **曲线应用**: 如果有曲线数据，使用曲线生成平滑过渡

### 随机闭眼参数
- `enable_random_blinks`: 是否启用（默认True）
- `random_blink_probability`: 概率0.0-1.0（默认0.3）
- `random_blink_min_interval`: 最小间隔帧数（默认10）
- `random_blink_max_interval`: 最大间隔帧数（默认30）

### 性能考虑
- 曲线数据在内存中缓存，不会重复计算
- 过渡帧生成是线性插值，计算效率高
- 支持任意长度的曲线数据
- 随机闭眼生成算法复杂度为O(n)，n为帧数

## 示例文件

查看 `curve_eyeball_usage_example.py` 获取完整的使用示例，包括：
- 基本曲线数据集成
- 贝塞尔曲线编辑器集成
- 手动曲线数据创建
- 结果应用到Maya对象

## 调试信息

启用曲线数据时，控制台会显示详细信息：
```
曲线数据支持: 0-1过渡=有(20点), 1-0过渡=有(20点)
  -> 将使用曲线数据生成平滑过渡帧，而非离散的0,1,0值
    生成0-1曲线过渡: 帧10-14 (5帧), 曲线点数: 20
      帧10: 进度0.00 -> 曲线索引0 -> 值0.000
      帧11: 进度0.25 -> 曲线索引4 -> 值0.200
      ...
```

## 兼容性

- 完全向后兼容现有代码
- 如果不提供曲线数据，行为与原来完全一致
- 支持部分曲线数据（只提供0-1或只提供1-0）
- 与现有的curve_eyeball_integration.py模块协同工作
