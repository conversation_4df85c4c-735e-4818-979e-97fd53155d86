# 大幅度/小幅度随机眨眼功能更新

## 概述

这个更新为随机眨眼功能添加了大幅度和小幅度眨眼的选择机制，让随机生成的眨眼更加自然和多样化。

## 新增功能

### 🎯 幅度选择机制
- **大幅度眨眼**: 遵循大幅眼动的时机规则
  - 第2或3帧输出1（眨眼结束）
  - 再+5或6帧输出0（眨眼周期完成）
  - 总持续时间：7-9帧

- **小幅度眨眼**: 遵循小幅眼动的时机规则
  - 第1或2帧输出1（眨眼结束）
  - 再+2或3帧输出0（眨眼周期完成）
  - 总持续时间：3-5帧

### 🔧 新增参数
- `random_large_amplitude_probability`: 随机闭眼中大幅度眨眼的概率（默认0.5，即50%）

## 使用方法

### 方法1：通过函数参数设置
```python
from analysis_eyeball_rotation import analyze_eyeball_with_curve_data

result = analyze_eyeball_with_curve_data(
    object_name="eyeball_joint",
    curve_data=curve_data,
    reversed_curve_data=reversed_curve_data,
    enable_random_blinks=True,
    random_blink_probability=0.3,  # 30%概率生成随机闭眼
    random_large_amplitude_probability=0.7,  # 70%概率为大幅度眨眼
    random_blink_min_interval=10,
    random_blink_max_interval=30
)
```

### 方法2：通过分析器设置
```python
from analysis_eyeball_rotation import AnalysisEyeballRotation

analyzer = AnalysisEyeballRotation(frame_matrix_dict=matrix_data)
analyzer.set_random_blink_settings(
    enable=True,
    probability=0.3,  # 30%概率
    min_interval=10,
    max_interval=30,
    large_amplitude_probability=0.8  # 80%概率为大幅度眨眼
)

result = analyzer.analyze_rotation_changes()
```

### 方法3：不同的幅度配置
```python
# 只有小幅度眨眼
analyzer.set_random_blink_settings(large_amplitude_probability=0.0)

# 只有大幅度眨眼
analyzer.set_random_blink_settings(large_amplitude_probability=1.0)

# 平衡配置
analyzer.set_random_blink_settings(large_amplitude_probability=0.5)

# 偏向大幅度
analyzer.set_random_blink_settings(large_amplitude_probability=0.8)
```

## 输出示例

### 小幅度随机眨眼
```python
{
    25: 0.000,    # 随机小幅度眨眼开始
    26: 1.000,    # 第1帧输出1（眨眼结束）
    28: 0.000     # +2帧输出0（眨眼完成）
}
```

### 大幅度随机眨眼
```python
{
    35: 0.000,    # 随机大幅度眨眼开始
    37: 1.000,    # 第2帧输出1（眨眼结束）
    42: 0.000     # +5帧输出0（眨眼完成）
}
```

### 混合曲线数据的随机眨眼
```python
{
    # 小幅度随机眨眼（曲线过渡）
    25: 0.000, 26: 0.500, 27: 1.000,    # 0-1过渡
    28: 0.700, 29: 0.300, 30: 0.000,    # 1-0过渡
    
    # 大幅度随机眨眼（曲线过渡）
    45: 0.000, 46: 0.200, 47: 0.600, 48: 1.000,    # 0-1过渡
    49: 0.800, 50: 0.500, 51: 0.200, 52: 0.000     # 1-0过渡
}
```

## 技术细节

### 幅度选择算法
1. **概率判断**: 使用`random_large_amplitude_probability`决定眨眼类型
2. **时机计算**: 根据眨眼类型应用相应的时机规则
3. **曲线应用**: 如果有曲线数据，在相应的帧范围内应用曲线过渡

### 参数验证
- `large_amplitude_probability`自动限制在0.0-1.0范围内
- 0.0 = 100%小幅度眨眼
- 1.0 = 100%大幅度眨眼
- 0.5 = 50%概率选择大幅度或小幅度

### 调试信息
启用随机闭眼时，控制台会显示详细信息：
```
=== 随机闭眼生成 ===
随机闭眼概率: 30%
间隔范围: 10-30帧
大幅度眨眼概率: 70%
小幅度眨眼概率: 30%
现有标记: 3个，占用帧: [8, 12, 16]

  ✓ 生成随机闭眼: 帧25-28 (曲线过渡, 小幅度眨眼)
    随机小幅度眨眼时机: 第1帧输出1，第3帧输出0
  ✓ 生成随机闭眼: 帧45-52 (曲线过渡, 大幅度眨眼)
    随机大幅度眨眼时机: 第3帧输出1，第8帧输出0
```

## 配置建议

### 自然眨眼配置
```python
# 模拟自然眨眼：大部分是小幅度，偶尔大幅度
analyzer.set_random_blink_settings(
    probability=0.3,  # 30%概率
    large_amplitude_probability=0.2,  # 20%大幅度，80%小幅度
    min_interval=15,
    max_interval=35
)
```

### 表情丰富配置
```python
# 表情丰富的角色：更多大幅度眨眼
analyzer.set_random_blink_settings(
    probability=0.4,  # 40%概率
    large_amplitude_probability=0.6,  # 60%大幅度，40%小幅度
    min_interval=10,
    max_interval=25
)
```

### 疲劳状态配置
```python
# 疲劳状态：更多大幅度眨眼，间隔更短
analyzer.set_random_blink_settings(
    probability=0.5,  # 50%概率
    large_amplitude_probability=0.8,  # 80%大幅度，20%小幅度
    min_interval=8,
    max_interval=20
)
```

## 测试验证

运行测试脚本验证功能：
```python
from test_amplitude_random_blinks import run_all_amplitude_tests
run_all_amplitude_tests()
```

## 兼容性

- ✅ 完全向后兼容现有代码
- ✅ 如果不设置`large_amplitude_probability`，默认为0.5（平衡配置）
- ✅ 与曲线数据功能完全兼容
- ✅ 与现有的冲突避免机制兼容

## 总结

这个更新让随机眨眼功能更加智能和自然：
- 🎯 **多样化**: 支持大幅度和小幅度两种眨眼类型
- 🔧 **可配置**: 可以精确控制两种类型的概率比例
- 📊 **符合规则**: 严格遵循原有的大幅眼动和小幅眼动时机规则
- 🎨 **平滑过渡**: 与曲线数据功能完美结合
- 🛡️ **智能避让**: 继续保持与检测到的眨眼的冲突避免

现在你可以根据不同的角色需求和场景要求，灵活配置随机眨眼的幅度分布，创造更加自然和生动的眨眼动画效果！
