#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试贝塞尔曲线编辑器的关键帧移动限制
"""

import sys
import os

# 添加路径以便导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from Qt import QtWidgets, QtCore
from bezier_curve_editor import BezierCurveEditor


class TestBezierConstraints(QtWidgets.QWidget):
    """测试贝塞尔曲线约束的窗口"""
    
    def __init__(self):
        super(TestBezierConstraints, self).__init__()
        self.setWindowTitle("贝塞尔曲线关键帧移动限制测试")
        self.setMinimumSize(1000, 700)
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QtWidgets.QVBoxLayout()
        
        # 说明文字
        info_label = QtWidgets.QLabel("""
<h3>贝塞尔曲线关键帧移动限制测试</h3>
<p><b>功能说明：</b></p>
<ul>
<li><b>第一个关键帧（最左边）：</b>只能上下移动，时间固定在最左边</li>
<li><b>第二个关键帧（中间）：</b>只能上下移动，时间固定在中间</li>
<li><b>第三个关键帧（最右边）：</b>只能上下移动，时间固定在最右边</li>
</ul>
<p><b>测试方法：</b></p>
<ol>
<li>尝试拖拽各个关键帧，观察移动限制</li>
<li>选中关键帧后，在右侧属性面板尝试修改时间值</li>
<li>修改时间范围，观察关键帧位置是否自动调整</li>
<li>使用重置曲线功能，观察关键帧是否正确定位</li>
</ol>
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 贝塞尔曲线编辑器
        self.bezier_editor = BezierCurveEditor()
        layout.addWidget(self.bezier_editor)
        
        # 测试按钮
        button_layout = QtWidgets.QHBoxLayout()
        
        test_time_range_btn = QtWidgets.QPushButton("测试时间范围变化")
        test_time_range_btn.clicked.connect(self.test_time_range_change)
        button_layout.addWidget(test_time_range_btn)
        
        test_reset_btn = QtWidgets.QPushButton("测试重置曲线")
        test_reset_btn.clicked.connect(self.test_reset_curve)
        button_layout.addWidget(test_reset_btn)
        
        print_positions_btn = QtWidgets.QPushButton("打印关键帧位置")
        print_positions_btn.clicked.connect(self.print_keyframe_positions)
        button_layout.addWidget(print_positions_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 初始打印
        self.print_keyframe_positions()
        
    def test_time_range_change(self):
        """测试时间范围变化"""
        print("\n=== 测试时间范围变化 ===")
        print("修改时间范围为 -5.0 到 15.0")
        
        # 修改时间范围
        self.bezier_editor.time_start_spinbox.setValue(-5.0)
        self.bezier_editor.time_end_spinbox.setValue(15.0)
        
        # 打印新的关键帧位置
        self.print_keyframe_positions()
        
    def test_reset_curve(self):
        """测试重置曲线"""
        print("\n=== 测试重置曲线 ===")
        self.bezier_editor.reset_curve()
        self.print_keyframe_positions()
        
    def print_keyframe_positions(self):
        """打印关键帧位置"""
        print("\n=== 当前关键帧位置 ===")
        time_range = self.bezier_editor.curve_widget.time_range
        print(f"时间范围: {time_range[0]} 到 {time_range[1]}")
        
        for i, kf in enumerate(self.bezier_editor.curve_widget.keyframes):
            position_desc = ""
            if i == 0:
                position_desc = "（应该在最左边）"
            elif i == 1:
                position_desc = "（应该在中间）"
            elif i == 2:
                position_desc = "（应该在最右边）"
            
            print(f"关键帧 {i+1}: 时间={kf.time:.3f}, 值={kf.value:.3f} {position_desc}")
        
        # 验证位置是否正确
        if len(self.bezier_editor.curve_widget.keyframes) >= 3:
            kf1, kf2, kf3 = self.bezier_editor.curve_widget.keyframes[:3]
            mid_time = (time_range[0] + time_range[1]) / 2
            
            print("\n=== 位置验证 ===")
            print(f"第一个关键帧时间 {kf1.time:.3f} == 最左边 {time_range[0]:.3f}: {abs(kf1.time - time_range[0]) < 0.001}")
            print(f"第二个关键帧时间 {kf2.time:.3f} == 中间 {mid_time:.3f}: {abs(kf2.time - mid_time) < 0.001}")
            print(f"第三个关键帧时间 {kf3.time:.3f} == 最右边 {time_range[1]:.3f}: {abs(kf3.time - time_range[1]) < 0.001}")


def main():
    """主函数"""
    app = QtWidgets.QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    test_window = TestBezierConstraints()
    test_window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
