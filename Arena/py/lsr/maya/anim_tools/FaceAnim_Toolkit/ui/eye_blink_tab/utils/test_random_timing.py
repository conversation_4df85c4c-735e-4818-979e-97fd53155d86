# -*- coding: utf-8 -*-

"""
测试随机眨眼时机逻辑
"""

import random

def test_random_blink_timing():
    """
    测试随机眨眼时机：
    - 小幅度眨眼：第0帧启动 → 随机1-2帧输出1 → 随机+2-3帧输出0
    - 大幅度眨眼：第0帧启动 → 随机2-3帧输出1 → 随机+5-6帧输出0
    """
    print("=== 测试随机眨眼时机逻辑 ===")
    
    # 参数设置
    frame_spacing = 2
    angle_threshold = 15
    amplitude_threshold = 1.5 * angle_threshold  # 22.5度
    cooldown_frames = 2 * frame_spacing
    
    print(f"参数设置:")
    print(f"- 帧间隔: {frame_spacing}")
    print(f"- 角度阈值: {angle_threshold}度")
    print(f"- 幅度阈值: {amplitude_threshold}度")
    print(f"- 冷却期: {cooldown_frames}帧")
    
    # 场景1：小幅度眨眼（幅度 < 22.5度）
    print(f"\n场景1：小幅度眨眼测试")
    rotation_data_small = {
        1: 5,   # 未超过阈值
        2: 8,   # 未超过阈值
        3: 20,  # 超过阈值，眨眼开始
        4: 18,  # 眨眼进行中，最大幅度20度 < 22.5度 = 小幅度眨眼
        5: 15,  # 可能输出1（随机第1或2帧）
        6: 12,  # 可能输出1（随机第1或2帧）
        7: 10,  # 可能输出0（随机+2或3帧）
        8: 8,   # 可能输出0（随机+2或3帧）
        9: 6,   # 可能输出0（随机+2或3帧）
        10: 5,  # 等待期
        11: 4,  # 等待期
        12: 3,  # 等待期
        13: 2,  # 等待期结束
    }
    
    # 场景2：大幅度眨眼（幅度 ≥ 22.5度）
    print(f"\n场景2：大幅度眨眼测试")
    rotation_data_large = {
        1: 5,   # 未超过阈值
        2: 8,   # 未超过阈值
        3: 20,  # 超过阈值，眨眼开始
        4: 25,  # 眨眼进行中，最大幅度30度 ≥ 22.5度 = 大幅度眨眼
        5: 30,  # 可能输出1（随机第2或3帧）
        6: 28,  # 可能输出1（随机第2或3帧）
        7: 25,  # 眨眼进行中
        8: 20,  # 眨眼进行中
        9: 15,  # 可能输出0（随机+5或6帧）
        10: 12, # 可能输出0（随机+5或6帧）
        11: 10, # 可能输出0（随机+5或6帧）
        12: 8,  # 等待期
        13: 6,  # 等待期
    }
    
    def simulate_random_timing(rotation_data, scenario_name, test_runs=5):
        print(f"\n--- {scenario_name} ---")
        
        results = []
        
        for run in range(test_runs):
            print(f"\n运行 {run + 1}:")
            
            result_markers = {}
            last_end_frame = None
            pending_blinks = {}
            
            for current_frame in sorted(rotation_data.keys()):
                rotation_diff = rotation_data[current_frame]
                
                # 处理待处理的眨眼标记
                blinks_to_remove = []
                for start_frame, blink_data in pending_blinks.items():
                    amplitude, end_offset, complete_offset = blink_data
                    frame_offset = current_frame - start_frame
                    
                    if amplitude < amplitude_threshold:
                        # 小幅度眨眼：随机时机（考虑偏移）
                        if frame_offset == end_offset:
                            output_frame = current_frame - frame_spacing
                            result_markers[output_frame] = 1
                            print(f"  帧 {current_frame}: 小幅度眨眼结束，在帧{output_frame}标记1（随机第{end_offset}帧）")
                        elif frame_offset == complete_offset:
                            output_frame = current_frame - frame_spacing
                            result_markers[output_frame] = 0
                            last_end_frame = current_frame
                            print(f"  帧 {current_frame}: 小幅度眨眼周期完成，在帧{output_frame}标记0（随机第{complete_offset}帧）")
                            blinks_to_remove.append(start_frame)
                    else:
                        # 大幅度眨眼：随机时机（考虑偏移）
                        if frame_offset == end_offset:
                            output_frame = current_frame - frame_spacing
                            result_markers[output_frame] = 1
                            print(f"  帧 {current_frame}: 大幅度眨眼结束，在帧{output_frame}标记1（随机第{end_offset}帧）")
                        elif frame_offset == complete_offset:
                            output_frame = current_frame - frame_spacing
                            result_markers[output_frame] = 0
                            last_end_frame = current_frame
                            print(f"  帧 {current_frame}: 大幅度眨眼周期完成，在帧{output_frame}标记0（随机第{complete_offset}帧）")
                            blinks_to_remove.append(start_frame)
                
                # 移除已处理完的眨眼
                for start_frame in blinks_to_remove:
                    del pending_blinks[start_frame]
                
                # 检查是否在冷却期内或有待处理的眨眼
                if (last_end_frame is not None and current_frame - last_end_frame < cooldown_frames) or pending_blinks:
                    continue
                
                # 判断当前是否超过阈值
                current_exceeds = rotation_diff > angle_threshold
                
                if current_exceeds:
                    # 检测到眨眼开始（考虑偏移）
                    blink_start_frame = current_frame - frame_spacing
                    result_markers[blink_start_frame] = 0
                    
                    # 模拟分析眨眼幅度
                    max_amplitude = max([rotation_data.get(current_frame + i, 0) 
                                       for i in range(min(10, len(rotation_data) - current_frame + 1))])
                    
                    # 生成随机时机
                    if max_amplitude < amplitude_threshold:
                        # 小幅度眨眼：随机第1或2帧输出1，然后随机+2或3帧输出0
                        end_offset = random.choice([1, 2])
                        complete_offset = end_offset + random.choice([2, 3])
                        blink_type = "小幅度"
                    else:
                        # 大幅度眨眼：随机第2或3帧输出1，然后随机+5或6帧输出0
                        end_offset = random.choice([2, 3])
                        complete_offset = end_offset + random.choice([5, 6])
                        blink_type = "大幅度"
                    
                    pending_blinks[current_frame] = (max_amplitude, end_offset, complete_offset)
                    
                    print(f"  帧 {current_frame}: 眨眼开始! 在帧{blink_start_frame}标记0")
                    print(f"       幅度: {max_amplitude:.1f}度 ({blink_type}眨眼)")
                    print(f"       随机时机: 第{end_offset}帧输出1，第{complete_offset}帧输出0")
            
            results.append(result_markers)
            print(f"  结果: {result_markers}")
        
        return results
    
    # 测试小幅度眨眼
    print(f"\n{'='*50}")
    small_results = simulate_random_timing(rotation_data_small, "小幅度眨眼场景", 3)
    
    # 测试大幅度眨眼
    print(f"\n{'='*50}")
    large_results = simulate_random_timing(rotation_data_large, "大幅度眨眼场景", 3)
    
    print(f"\n=== 随机性验证 ===")
    print(f"小幅度眨眼多次运行结果:")
    for i, result in enumerate(small_results):
        print(f"  运行{i+1}: {result}")
    
    print(f"\n大幅度眨眼多次运行结果:")
    for i, result in enumerate(large_results):
        print(f"  运行{i+1}: {result}")
    
    # 检查随机性
    small_unique = len(set(str(r) for r in small_results))
    large_unique = len(set(str(r) for r in large_results))
    
    print(f"\n随机性分析:")
    print(f"- 小幅度眨眼: {small_unique}/{len(small_results)} 种不同结果")
    print(f"- 大幅度眨眼: {large_unique}/{len(large_results)} 种不同结果")
    
    if small_unique > 1 or large_unique > 1:
        print("✅ 随机时机逻辑工作正常！")
    else:
        print("⚠️  随机性可能需要更多测试运行来验证")


if __name__ == "__main__":
    test_random_blink_timing()
