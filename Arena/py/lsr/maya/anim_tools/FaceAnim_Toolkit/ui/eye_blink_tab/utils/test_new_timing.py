# -*- coding: utf-8 -*-

"""
测试新的眨眼时机逻辑
"""

def test_new_blink_timing():
    """
    测试新的眨眼时机：
    - 小幅度眨眼：第0帧启动 → 第1帧输出1 → 第3帧输出0
    - 大幅度眨眼：第0帧启动 → 第2帧输出1 → 第6帧输出0
    """
    print("=== 测试新的眨眼时机逻辑 ===")
    
    # 参数设置
    frame_spacing = 2
    angle_threshold = 15
    amplitude_threshold = 4.5 * angle_threshold  # 67.5度
    cooldown_frames = 2 * frame_spacing
    
    print(f"参数设置:")
    print(f"- 帧间隔: {frame_spacing}")
    print(f"- 角度阈值: {angle_threshold}度")
    print(f"- 幅度阈值: {amplitude_threshold}度")
    print(f"- 冷却期: {cooldown_frames}帧")
    
    # 场景1：小幅度眨眼（幅度 < 67.5度）
    print(f"\n场景1：小幅度眨眼测试")
    rotation_data_small = {
        1: 5,   # 未超过阈值
        2: 8,   # 未超过阈值
        3: 20,  # 超过阈值，眨眼开始，幅度分析后续10帧
        4: 25,  # 眨眼进行中，最大幅度60度 < 67.5度 = 小幅度眨眼
        5: 35,  # 应该输出1（小幅度眨眼结束，第1帧）
        6: 40,  # 眨眼进行中
        7: 30,  # 应该输出0（小幅度眨眼周期完成，第3帧）
        8: 20,  # 等待期
        9: 15,  # 等待期
        10: 10, # 等待期
        11: 8,  # 等待期结束
        12: 25, # 新的眨眼开始
        13: 60, # 最大幅度60度
    }
    
    # 场景2：大幅度眨眼（幅度 ≥ 67.5度）
    print(f"\n场景2：大幅度眨眼测试")
    rotation_data_large = {
        1: 5,   # 未超过阈值
        2: 8,   # 未超过阈值
        3: 20,  # 超过阈值，眨眼开始，幅度分析后续10帧
        4: 30,  # 眨眼进行中，最大幅度80度 ≥ 67.5度 = 大幅度眨眼
        5: 50,  # 应该输出1（大幅度眨眼结束，第2帧）
        6: 70,  # 眨眼进行中
        7: 80,  # 眨眼进行中
        8: 60,  # 眨眼进行中
        9: 40,  # 应该输出0（大幅度眨眼周期完成，第6帧）
        10: 20, # 等待期
        11: 15, # 等待期
        12: 10, # 等待期
        13: 8,  # 等待期结束
    }
    
    def simulate_new_timing(rotation_data, scenario_name):
        print(f"\n--- {scenario_name} ---")
        
        result_markers = {}
        last_end_frame = None
        pending_blinks = {}
        
        for current_frame in sorted(rotation_data.keys()):
            rotation_diff = rotation_data[current_frame]
            
            # 处理待处理的眨眼标记
            blinks_to_remove = []
            for start_frame, amplitude in pending_blinks.items():
                frame_offset = current_frame - start_frame
                
                if amplitude < amplitude_threshold:
                    # 小幅度眨眼：第1帧输出1，第3帧输出0（考虑偏移）
                    if frame_offset == 1:
                        output_frame = current_frame - frame_spacing
                        result_markers[output_frame] = 1
                        print(f"帧 {current_frame}: 小幅度眨眼结束，在帧{output_frame}标记1（偏移修正）")
                    elif frame_offset == 3:
                        output_frame = current_frame - frame_spacing
                        result_markers[output_frame] = 0
                        last_end_frame = current_frame
                        print(f"帧 {current_frame}: 小幅度眨眼周期完成，在帧{output_frame}标记0（偏移修正）")
                        blinks_to_remove.append(start_frame)
                else:
                    # 大幅度眨眼：第2帧输出1，第6帧输出0（考虑偏移）
                    if frame_offset == 2:
                        output_frame = current_frame - frame_spacing
                        result_markers[output_frame] = 1
                        print(f"帧 {current_frame}: 大幅度眨眼结束，在帧{output_frame}标记1（偏移修正）")
                    elif frame_offset == 6:
                        output_frame = current_frame - frame_spacing
                        result_markers[output_frame] = 0
                        last_end_frame = current_frame
                        print(f"帧 {current_frame}: 大幅度眨眼周期完成，在帧{output_frame}标记0（偏移修正）")
                        blinks_to_remove.append(start_frame)
            
            # 移除已处理完的眨眼
            for start_frame in blinks_to_remove:
                del pending_blinks[start_frame]
            
            # 检查是否在冷却期内或有待处理的眨眼
            if (last_end_frame is not None and current_frame - last_end_frame < cooldown_frames) or pending_blinks:
                if pending_blinks:
                    print(f"帧 {current_frame}: 有待处理的眨眼，跳过新检测")
                else:
                    print(f"帧 {current_frame}: 旋转差异={rotation_diff:.1f}度 -> 在等待期内，跳过检测")
                continue
            
            # 判断当前是否超过阈值
            current_exceeds = rotation_diff > angle_threshold
            
            if current_exceeds:
                # 检测到眨眼开始（考虑偏移）
                blink_start_frame = current_frame - frame_spacing
                result_markers[blink_start_frame] = 0
                
                # 模拟分析眨眼幅度
                max_amplitude = max([rotation_data.get(current_frame + i, 0) 
                                   for i in range(min(10, len(rotation_data) - current_frame + 1))])
                
                pending_blinks[current_frame] = max_amplitude
                
                blink_type = "小幅度" if max_amplitude < amplitude_threshold else "大幅度"
                print(f"帧 {current_frame}: 眨眼开始! 在帧{blink_start_frame}标记0（偏移修正）")
                print(f"     最大幅度: {max_amplitude:.1f}度 ({blink_type}眨眼)")
            else:
                print(f"帧 {current_frame}: 旋转差异={rotation_diff:.1f}度 -> 未超过阈值")
        
        return result_markers
    
    # 测试小幅度眨眼
    small_result = simulate_new_timing(rotation_data_small, "小幅度眨眼场景")
    
    # 测试大幅度眨眼
    large_result = simulate_new_timing(rotation_data_large, "大幅度眨眼场景")
    
    print(f"\n=== 测试结果 ===")
    print(f"小幅度眨眼结果: {small_result}")
    print(f"大幅度眨眼结果: {large_result}")
    
    # 验证结果
    print(f"\n=== 结果验证 ===")
    
    # 小幅度眨眼预期（偏移修正后）：帧1=0(开始), 帧2=1(第1帧结束), 帧4=0(第3帧完成)
    small_expected = {1: 0, 2: 1, 4: 0}
    print(f"小幅度眨眼预期（偏移修正）: {small_expected}")
    print(f"小幅度眨眼实际: {small_result}")
    
    # 大幅度眨眼预期（偏移修正后）：帧1=0(开始), 帧3=1(第2帧结束), 帧7=0(第6帧完成)
    large_expected = {1: 0, 3: 1, 7: 0}
    print(f"大幅度眨眼预期（偏移修正）: {large_expected}")
    print(f"大幅度眨眼实际: {large_result}")
    
    print(f"\n新时机说明:")
    print(f"- 小幅度眨眼: 第0帧启动 → 第1帧输出1 → 第3帧输出0")
    print(f"- 大幅度眨眼: 第0帧启动 → 第2帧输出1 → 第6帧输出0")
    print(f"- 所有标记都向前偏移{frame_spacing}帧，与实际眼动时机同步")
    
    # 检查核心逻辑（只检查第一次眨眼）
    small_core = {k: v for k, v in small_result.items() if k <= 5}
    large_core = {k: v for k, v in large_result.items() if k <= 8}
    
    print(f"\n核心逻辑验证:")
    print(f"小幅度眨眼核心: {small_core}")
    print(f"大幅度眨眼核心: {large_core}")

    if small_core == small_expected and large_core == large_expected:
        print("✅ 测试通过！新的眨眼时机逻辑正确")
        print("✅ 小幅度眨眼：第1帧输出1，第3帧输出0")
        print("✅ 大幅度眨眼：第2帧输出1，第6帧输出0")
        print("✅ 时间偏移修正正确，标记与实际眼动时机同步")
    else:
        print("❌ 测试失败！需要调整逻辑")


if __name__ == "__main__":
    test_new_blink_timing()
