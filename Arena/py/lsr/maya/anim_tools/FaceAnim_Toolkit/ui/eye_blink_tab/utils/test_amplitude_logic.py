# -*- coding: utf-8 -*-

"""
测试新的眨眼幅度检测逻辑
"""

def test_amplitude_blink_logic():
    """
    模拟眨眼幅度检测逻辑的测试
    """
    print("=== 测试眨眼幅度检测逻辑 ===")
    
    # 模拟旋转差异数据（度）
    # 场景1：快速眨眼（幅度 < 1.5倍阈值 = 22.5度）
    print("\n场景1：快速眨眼测试")
    rotation_data_fast = {
        1: 5,   # 未超过阈值
        2: 8,   # 未超过阈值
        3: 20,  # 超过阈值，眨眼开始，幅度分析后续10帧
        4: 18,  # 眨眼进行中，最大幅度22度 < 22.5度 = 快速眨眼
        5: 22,  # 应该输出1（快速眨眼结束，第2帧）
        6: 15,  # 眨眼进行中
        7: 10,  # 应该输出0（快速眨眼周期完成，第4帧）
        8: 8,   # 等待期
        9: 5,   # 等待期
        10: 6,  # 等待期
        11: 7,  # 等待期结束
        12: 8,  # 等待期结束后的正常值
        13: 20, # 新的眨眼开始
    }

    # 场景2：慢速眨眼（幅度 ≥ 1.5倍阈值 = 22.5度）
    print("\n场景2：慢速眨眼测试")
    rotation_data_slow = {
        1: 5,   # 未超过阈值
        2: 8,   # 未超过阈值
        3: 20,  # 超过阈值，眨眼开始，幅度分析后续10帧
        4: 25,  # 眨眼进行中，最大幅度30度 ≥ 22.5度 = 慢速眨眼
        5: 30,  # 眨眼进行中
        6: 28,  # 应该输出1（慢速眨眼结束，第3帧）
        7: 25,  # 眨眼进行中
        8: 20,  # 眨眼进行中
        9: 15,  # 应该输出0（慢速眨眼周期完成，第6帧）
        10: 10, # 等待期
        11: 8,  # 等待期
        12: 6,  # 等待期
        13: 5,  # 等待期结束
    }
    
    # 参数设置
    frame_spacing = 2
    angle_threshold = 15
    cooldown_frames = 2 * frame_spacing
    
    def simulate_blink_detection(rotation_data, scenario_name):
        print(f"\n--- {scenario_name} ---")
        print(f"参数: 帧间隔={frame_spacing}, 阈值={angle_threshold}度, 冷却期={cooldown_frames}帧")
        
        result_markers = {}
        last_end_frame = None
        pending_blinks = {}
        
        for current_frame in sorted(rotation_data.keys()):
            rotation_diff = rotation_data[current_frame]
            
            # 处理待处理的眨眼标记
            blinks_to_remove = []
            for start_frame, amplitude in pending_blinks.items():
                frame_offset = current_frame - start_frame
                amplitude_threshold = 1.5 * angle_threshold  # 22.5度
                
                if amplitude < amplitude_threshold:
                    # 快速眨眼：第2帧输出1，第4帧输出0（考虑偏移）
                    if frame_offset == 2:
                        output_frame = current_frame - frame_spacing
                        result_markers[output_frame] = 1
                        print(f"帧 {current_frame}: 快速眨眼结束，在帧{output_frame}标记1（偏移修正）")
                    elif frame_offset == 4:
                        output_frame = current_frame - frame_spacing
                        result_markers[output_frame] = 0
                        last_end_frame = current_frame
                        print(f"帧 {current_frame}: 快速眨眼周期完成，在帧{output_frame}标记0（偏移修正）")
                        blinks_to_remove.append(start_frame)
                else:
                    # 慢速眨眼：第3帧输出1，第6帧输出0（考虑偏移）
                    if frame_offset == 3:
                        output_frame = current_frame - frame_spacing
                        result_markers[output_frame] = 1
                        print(f"帧 {current_frame}: 慢速眨眼结束，在帧{output_frame}标记1（偏移修正）")
                    elif frame_offset == 6:
                        output_frame = current_frame - frame_spacing
                        result_markers[output_frame] = 0
                        last_end_frame = current_frame
                        print(f"帧 {current_frame}: 慢速眨眼周期完成，在帧{output_frame}标记0（偏移修正）")
                        blinks_to_remove.append(start_frame)
            
            # 移除已处理完的眨眼
            for start_frame in blinks_to_remove:
                del pending_blinks[start_frame]
            
            # 检查是否在冷却期内或有待处理的眨眼
            if (last_end_frame is not None and current_frame - last_end_frame < cooldown_frames) or pending_blinks:
                if pending_blinks:
                    print(f"帧 {current_frame}: 有待处理的眨眼，跳过新检测")
                else:
                    print(f"帧 {current_frame}: 旋转差异={rotation_diff:.1f}度 -> 在等待期内，跳过检测")
                continue

            # 判断当前是否超过阈值
            current_exceeds = rotation_diff > angle_threshold

            if current_exceeds:
                # 检测到眨眼开始（考虑偏移）
                blink_start_frame = current_frame - frame_spacing
                result_markers[blink_start_frame] = 0

                # 模拟分析眨眼幅度（简化版）
                # 在实际代码中，这会分析后续10帧的最大幅度
                max_amplitude = max([rotation_data.get(current_frame + i, 0)
                                   for i in range(min(10, len(rotation_data) - current_frame + 1))])

                pending_blinks[current_frame] = max_amplitude

                amplitude_threshold = 1.5 * angle_threshold
                blink_type = "快速" if max_amplitude < amplitude_threshold else "慢速"
                print(f"帧 {current_frame}: 眨眼开始! 在帧{blink_start_frame}标记0（偏移修正）")
                print(f"     最大幅度: {max_amplitude:.1f}度 ({blink_type}眨眼)")
            else:
                print(f"帧 {current_frame}: 旋转差异={rotation_diff:.1f}度 -> 未超过阈值")
        
        return result_markers
    
    # 测试快速眨眼
    fast_result = simulate_blink_detection(rotation_data_fast, "快速眨眼场景")
    
    # 测试慢速眨眼
    slow_result = simulate_blink_detection(rotation_data_slow, "慢速眨眼场景")
    
    print(f"\n=== 测试结果 ===")
    print(f"快速眨眼结果: {fast_result}")
    print(f"慢速眨眼结果: {slow_result}")
    
    # 验证结果
    print(f"\n=== 结果验证 ===")
    
    # 快速眨眼预期（偏移修正后）：帧1=0(开始), 帧3=1(结束), 帧5=0(完成)
    fast_expected = {1: 0, 3: 1, 5: 0}
    print(f"快速眨眼预期（偏移修正）: {fast_expected}")
    print(f"快速眨眼实际: {fast_result}")

    # 慢速眨眼预期（偏移修正后）：帧1=0(开始), 帧4=1(结束), 帧7=0(完成)
    slow_expected = {1: 0, 4: 1, 7: 0}
    print(f"慢速眨眼预期（偏移修正）: {slow_expected}")
    print(f"慢速眨眼实际: {slow_result}")

    print(f"\n偏移说明:")
    print(f"- 帧间隔为{frame_spacing}，所有标记都向前偏移{frame_spacing}帧")
    print(f"- 这样眨眼标记与实际眼动时机同步")
    
    # 检查核心逻辑是否正确（忽略额外的眨眼检测）
    fast_core = {k: v for k, v in fast_result.items() if k <= 10}  # 只检查前10帧
    slow_core = {k: v for k, v in slow_result.items() if k <= 10}  # 只检查前10帧

    print(f"\n核心逻辑验证:")
    print(f"快速眨眼核心: {fast_core}")
    print(f"慢速眨眼核心: {slow_core}")

    if fast_core == fast_expected and slow_core == slow_expected:
        print("✅ 测试通过！幅度检测逻辑正确")
        print("✅ 快速眨眼：第2帧输出1，第4帧输出0")
        print("✅ 慢速眨眼：第3帧输出1，第6帧输出0")
    else:
        print("❌ 测试失败！需要调整逻辑")


if __name__ == "__main__":
    test_amplitude_blink_logic()
