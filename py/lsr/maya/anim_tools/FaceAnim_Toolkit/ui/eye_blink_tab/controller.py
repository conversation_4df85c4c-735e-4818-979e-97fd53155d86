# -*- coding: utf-8 -*-
"""
贝塞尔曲线K帧编辑器主窗口
"""

from functools import partial
from Qt import QtWidgets
from lsr.maya.anim_tools.FaceAnim_Toolkit.ui.eye_blink_tab.ui.BezierCurveWidget.controller import BezierCurveWidget


class BezierCurveEditor(QtWidgets.QWidget):
    """贝塞尔曲线K帧编辑器主窗口"""

    def __init__(self, parent=None):
        super(BezierCurveEditor, self).__init__(parent)
        self.setWindowTitle("贝塞尔曲线K帧编辑器")
        self.setMinimumSize(800, 600)
        self._init_ui()
        # self._connect_signals()

    def _init_ui(self):
        """初始化UI"""
        main_layout = QtWidgets.QHBoxLayout(self)
        self.setLayout(main_layout)

        # 左侧：曲线编辑器
        left_widget = QtWidgets.QWidget()
        left_layout = QtWidgets.QVBoxLayout()

        # 工具栏
        toolbar = self.create_toolbar()
        left_layout.addWidget(toolbar)

        # 曲线编辑器
        self.curve_widget = BezierCurveWidget()
        left_layout.addWidget(self.curve_widget)

        # 时间轴控制
        # time_control = self.create_time_control()
        # left_layout.addWidget(time_control)

        left_widget.setLayout(left_layout)
        main_layout.addWidget(left_widget, 3)

        # 右侧：属性面板
        right_widget = QtWidgets.QWidget()
        right_layout = QtWidgets.QVBoxLayout()

        # 关键帧属性
        # properties_group = QtWidgets.QGroupBox("关键帧属性")
        # self.properties_widget = KeyframePropertiesWidget()
        # 设置曲线编辑器引用
        # self.properties_widget.set_curve_widget(self.curve_widget)
        # properties_layout = QtWidgets.QVBoxLayout()
        # properties_layout.addWidget(self.properties_widget)
        # properties_group.setLayout(properties_layout)
        # right_layout.addWidget(properties_group)
        #
        # # 曲线设置
        # settings_group = self.create_settings_group()
        # right_layout.addWidget(settings_group)
        #
        # # 眼动集成
        # integration_group = self.create_eyeball_integration_group()
        # right_layout.addWidget(integration_group)
        #
        # right_layout.addStretch()
        # right_widget.setLayout(right_layout)
        # main_layout.addWidget(right_widget, 1)

        self.setLayout(main_layout)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QtWidgets.QWidget()
        layout = QtWidgets.QHBoxLayout()

        # 添加关键帧按钮
        self.add_keyframe_btn = QtWidgets.QPushButton("添加关键帧 (Ctrl+A)")
        self.add_keyframe_btn.setToolTip("在时间轴中间添加关键帧\n快捷键: Ctrl+A")
        self.add_keyframe_btn.clicked.connect(partial(self.add_keyframe))
        layout.addWidget(self.add_keyframe_btn)

        # 删除关键帧按钮
        self.delete_keyframe_btn = QtWidgets.QPushButton("删除关键帧 (Del)")
        self.delete_keyframe_btn.setToolTip("删除选中的关键帧\n快捷键: Delete")
        self.delete_keyframe_btn.clicked.connect(partial(self.delete_keyframe))
        layout.addWidget(self.delete_keyframe_btn)

        layout.addWidget(QtWidgets.QLabel("|"))

        # 撤销按钮
        self.undo_btn = QtWidgets.QPushButton("撤销 (Ctrl+Z)")
        self.undo_btn.setToolTip("撤销上一步操作\n快捷键: Ctrl+Z")
        self.undo_btn.clicked.connect(partial(self.undo_action))
        layout.addWidget(self.undo_btn)

        # 重做按钮
        self.redo_btn = QtWidgets.QPushButton("重做 (Ctrl+Y)")
        self.redo_btn.setToolTip("重做上一步操作\n快捷键: Ctrl+Y")
        self.redo_btn.clicked.connect(partial(self.redo_action))
        layout.addWidget(self.redo_btn)

        layout.addWidget(QtWidgets.QLabel("|"))

        # 网格开关
        self.grid_checkbox = QtWidgets.QCheckBox("显示网格")
        self.grid_checkbox.setChecked(True)
        self.grid_checkbox.setToolTip("显示/隐藏网格线")
        self.grid_checkbox.toggled.connect(partial(self.toggle_grid))
        layout.addWidget(self.grid_checkbox)

        layout.addStretch()
        toolbar.setLayout(layout)
        return toolbar

    def add_keyframe(self, *args, **kwargs):
        """添加关键帧"""
        # 在时间轴中间添加关键帧
        time_range = self.curve_widget.time_range
        mid_time = (time_range[0] + time_range[1]) / 2
        self.curve_widget.add_keyframe(mid_time, 0.0)

    def delete_keyframe(self, *args, **kwargs):
        """删除选中的关键帧"""
        if self.curve_widget.selected_keyframe:
            self.curve_widget.remove_keyframe(self.curve_widget.selected_keyframe)

    def undo_action(self, *args, **kwargs):
        """撤销操作"""
        if self.curve_widget.undo():
            # 更新属性面板
            self.properties_widget.set_keyframe(self.curve_widget.selected_keyframe)

    def redo_action(self, *args, **kwargs):
        """重做操作"""
        if self.curve_widget.redo():
            # 更新属性面板
            self.properties_widget.set_keyframe(self.curve_widget.selected_keyframe)

    def toggle_grid(self, enabled, *args, **kwargs):
        """切换网格显示"""
        self.curve_widget.grid_enabled = enabled
        self.curve_widget.update()


# class BezierCurveEditor(QtWidgets.QWidget):
#     """贝塞尔曲线K帧编辑器主窗口"""
#
#     def __init__(self, parent=None):
#         super(BezierCurveEditor, self).__init__(parent)
#         self.setWindowTitle("贝塞尔曲线K帧编辑器")
#         self.setMinimumSize(800, 600)
#         self._init_ui()
#         # self._connect_signals()
#
#     def _init_ui(self):
#         """初始化UI"""
#         main_layout = QtWidgets.QHBoxLayout(self)
#         self.setLayout(main_layout)
#
#         # 左侧：曲线编辑器
#         left_widget = QtWidgets.QWidget()
#         left_layout = QtWidgets.QVBoxLayout()
#
#         # 工具栏
#         toolbar = self.create_toolbar()
#         left_layout.addWidget(toolbar)
#
#         # 曲线编辑器
#         self.curve_widget = BezierCurveWidget()
#         left_layout.addWidget(self.curve_widget)
#
#         # 时间轴控制
#         time_control = self.create_time_control()
#         left_layout.addWidget(time_control)
#
#         left_widget.setLayout(left_layout)
#         main_layout.addWidget(left_widget, 3)
#
#         # 右侧：属性面板
#         right_widget = QtWidgets.QWidget()
#         right_layout = QtWidgets.QVBoxLayout()
#
#         # 关键帧属性
#         properties_group = QtWidgets.QGroupBox("关键帧属性")
#         self.properties_widget = KeyframePropertiesWidget()
#         # 设置曲线编辑器引用
#         self.properties_widget.set_curve_widget(self.curve_widget)
#         properties_layout = QtWidgets.QVBoxLayout()
#         properties_layout.addWidget(self.properties_widget)
#         properties_group.setLayout(properties_layout)
#         right_layout.addWidget(properties_group)
#
#         # 曲线设置
#         settings_group = self.create_settings_group()
#         right_layout.addWidget(settings_group)
#
#         # 眼动集成
#         integration_group = self.create_eyeball_integration_group()
#         right_layout.addWidget(integration_group)
#
#         right_layout.addStretch()
#         right_widget.setLayout(right_layout)
#         main_layout.addWidget(right_widget, 1)
#
#         self.setLayout(main_layout)
#
#     def create_toolbar(self):
#         """创建工具栏"""
#         toolbar = QtWidgets.QWidget()
#         layout = QtWidgets.QHBoxLayout()
#
#         # 添加关键帧按钮
#         self.add_keyframe_btn = QtWidgets.QPushButton("添加关键帧 (Ctrl+A)")
#         self.add_keyframe_btn.setToolTip("在时间轴中间添加关键帧\n快捷键: Ctrl+A")
#         self.add_keyframe_btn.clicked.connect(self.add_keyframe)
#         layout.addWidget(self.add_keyframe_btn)
#
#         # 删除关键帧按钮
#         self.delete_keyframe_btn = QtWidgets.QPushButton("删除关键帧 (Del)")
#         self.delete_keyframe_btn.setToolTip("删除选中的关键帧\n快捷键: Delete")
#         self.delete_keyframe_btn.clicked.connect(self.delete_keyframe)
#         layout.addWidget(self.delete_keyframe_btn)
#
#         layout.addWidget(QtWidgets.QLabel("|"))
#
#         # 撤销按钮
#         self.undo_btn = QtWidgets.QPushButton("撤销 (Ctrl+Z)")
#         self.undo_btn.setToolTip("撤销上一步操作\n快捷键: Ctrl+Z")
#         self.undo_btn.clicked.connect(self.undo_action)
#         layout.addWidget(self.undo_btn)
#
#         # 重做按钮
#         self.redo_btn = QtWidgets.QPushButton("重做 (Ctrl+Y)")
#         self.redo_btn.setToolTip("重做上一步操作\n快捷键: Ctrl+Y")
#         self.redo_btn.clicked.connect(self.redo_action)
#         layout.addWidget(self.redo_btn)
#
#         layout.addWidget(QtWidgets.QLabel("|"))
#
#         # 网格开关
#         self.grid_checkbox = QtWidgets.QCheckBox("显示网格")
#         self.grid_checkbox.setChecked(True)
#         self.grid_checkbox.setToolTip("显示/隐藏网格线")
#         self.grid_checkbox.toggled.connect(self.toggle_grid)
#         layout.addWidget(self.grid_checkbox)
#
#         layout.addStretch()
#         toolbar.setLayout(layout)
#         return toolbar
#
#     def create_time_control(self):
#         """创建时间轴控制"""
#         widget = QtWidgets.QWidget()
#         layout = QtWidgets.QHBoxLayout()
#
#         layout.addWidget(QtWidgets.QLabel("时间范围:"))
#
#         self.time_start_spinbox = QtWidgets.QDoubleSpinBox()
#         self.time_start_spinbox.setRange(-999.0, 999.0)
#         self.time_start_spinbox.setValue(0.0)
#         self.time_start_spinbox.valueChanged.connect(self.update_time_range)
#         layout.addWidget(self.time_start_spinbox)
#
#         layout.addWidget(QtWidgets.QLabel("到"))
#
#         self.time_end_spinbox = QtWidgets.QDoubleSpinBox()
#         self.time_end_spinbox.setRange(-999.0, 999.0)
#         self.time_end_spinbox.setValue(10.0)
#         self.time_end_spinbox.valueChanged.connect(self.update_time_range)
#         layout.addWidget(self.time_end_spinbox)
#
#         layout.addWidget(QtWidgets.QLabel("|"))
#         layout.addWidget(QtWidgets.QLabel("值范围:"))
#
#         self.value_min_spinbox = QtWidgets.QDoubleSpinBox()
#         self.value_min_spinbox.setRange(-999.0, 999.0)
#         self.value_min_spinbox.setValue(-1.0)
#         self.value_min_spinbox.valueChanged.connect(self.update_value_range)
#         layout.addWidget(self.value_min_spinbox)
#
#         layout.addWidget(QtWidgets.QLabel("到"))
#
#         self.value_max_spinbox = QtWidgets.QDoubleSpinBox()
#         self.value_max_spinbox.setRange(-999.0, 999.0)
#         self.value_max_spinbox.setValue(1.0)
#         self.value_max_spinbox.valueChanged.connect(self.update_value_range)
#         layout.addWidget(self.value_max_spinbox)
#
#         layout.addStretch()
#         widget.setLayout(layout)
#         return widget
#
#     def create_settings_group(self):
#         """创建设置组"""
#         group = QtWidgets.QGroupBox("曲线设置")
#         layout = QtWidgets.QVBoxLayout()
#
#         # 关键帧切线预设
#         tangent_layout = QtWidgets.QHBoxLayout()
#         tangent_layout.addWidget(QtWidgets.QLabel("切线预设:"))
#         self.tangent_preset_combo = QtWidgets.QComboBox()
#         self.tangent_preset_combo.addItems(["自由", "线性", "缓入", "缓出", "缓入缓出", "弹跳"])
#         self.tangent_preset_combo.currentIndexChanged.connect(self.apply_tangent_preset)
#         tangent_layout.addWidget(self.tangent_preset_combo)
#         layout.addLayout(tangent_layout)
#
#         # 应用到所有关键帧
#         apply_all_btn = QtWidgets.QPushButton("应用到所有关键帧")
#         apply_all_btn.clicked.connect(self.apply_preset_to_all_keyframes)
#         layout.addWidget(apply_all_btn)
#
#         # 重置曲线按钮
#         reset_btn = QtWidgets.QPushButton("重置曲线")
#         reset_btn.clicked.connect(self.reset_curve)
#         layout.addWidget(reset_btn)
#
#         group.setLayout(layout)
#         return group
#
#     def create_presets_group(self):
#         """创建预设组"""
#         group = QtWidgets.QGroupBox("预设曲线")
#         layout = QtWidgets.QVBoxLayout()
#
#         # 预设按钮
#         presets = [
#             ("线性", self.apply_linear_preset),
#             ("缓入", self.apply_ease_in_preset),
#             ("缓出", self.apply_ease_out_preset),
#             ("缓入缓出", self.apply_ease_in_out_preset),
#             ("弹跳", self.apply_bounce_preset),
#             ("眨眼动画", self.create_eyeball_animation_preset),
#             ("重置", self.reset_curve)
#         ]
#
#         for name, callback in presets:
#             btn = QtWidgets.QPushButton(name)
#             btn.clicked.connect(callback)
#             layout.addWidget(btn)
#
#         group.setLayout(layout)
#         return group
#
#     def create_eyeball_integration_group(self):
#         """创建眼动集成控制组"""
#         group = QtWidgets.QGroupBox("眼动集成")
#         layout = QtWidgets.QVBoxLayout()
#
#         # 采样设置
#         sample_layout = QtWidgets.QHBoxLayout()
#         sample_layout.addWidget(QtWidgets.QLabel("采样点数:"))
#         self.sample_spinbox = QtWidgets.QSpinBox()
#         self.sample_spinbox.setRange(10, 1000)
#         self.sample_spinbox.setValue(100)
#         self.sample_spinbox.setToolTip("曲线采样点数量，影响动画精度")
#         sample_layout.addWidget(self.sample_spinbox)
#         layout.addLayout(sample_layout)
#
#         # 归一化选项
#         self.normalize_checkbox = QtWidgets.QCheckBox("归一化到0-1范围")
#         self.normalize_checkbox.setChecked(True)
#         self.normalize_checkbox.setToolTip("将曲线值映射到0-1范围")
#         layout.addWidget(self.normalize_checkbox)
#
#         # 反转选项
#         self.reverse_checkbox = QtWidgets.QCheckBox("反转曲线(1-0)")
#         self.reverse_checkbox.setChecked(False)
#         self.reverse_checkbox.setToolTip("反转曲线值，用于相反的动画效果")
#         layout.addWidget(self.reverse_checkbox)
#
#         # 属性设置
#         attr_layout = QtWidgets.QHBoxLayout()
#         attr_layout.addWidget(QtWidgets.QLabel("属性:"))
#         self.attribute_combo = QtWidgets.QComboBox()
#         self.attribute_combo.addItems(["scaleY", "scaleX", "scaleZ", "rotateX", "rotateY", "rotateZ", "visibility"])
#         self.attribute_combo.setToolTip("要应用动画的属性")
#         attr_layout.addWidget(self.attribute_combo)
#         layout.addLayout(attr_layout)
#
#         # 导出按钮
#         export_btn = QtWidgets.QPushButton("导出到Maya")
#         export_btn.setToolTip("将曲线数据导出到Maya选中对象")
#         export_btn.clicked.connect(self.export_curve_to_maya)
#         layout.addWidget(export_btn)
#
#         # 预览按钮
#         preview_btn = QtWidgets.QPushButton("预览数据")
#         preview_btn.setToolTip("在控制台预览曲线数据")
#         preview_btn.clicked.connect(self.preview_curve_data)
#         layout.addWidget(preview_btn)
#
#         group.setLayout(layout)
#         return group
#
#     def export_curve_to_maya(self):
#         """导出曲线到Maya"""
#         num_samples = self.sample_spinbox.value()
#         normalized = self.normalize_checkbox.isChecked()
#         reversed = self.reverse_checkbox.isChecked()
#         attribute = self.attribute_combo.currentText()
#
#         success = self.export_curve_data_to_maya(
#             object_name=None,  # 使用选中对象
#             attribute_name=attribute,
#             num_samples=num_samples,
#             normalized=normalized,
#             reversed=reversed
#         )
#
#         if success:
#             print(f"✅ 曲线数据已成功导出到属性 '{attribute}'")
#         else:
#             print("❌ 导出失败，请检查Maya环境和选中对象")
#
#     def preview_curve_data(self):
#         """预览曲线数据"""
#         num_samples = self.sample_spinbox.value()
#         normalized = self.normalize_checkbox.isChecked()
#         reversed = self.reverse_checkbox.isChecked()
#
#         curve_data = self.get_curve_integration_data(num_samples, normalized, reversed)
#
#         if curve_data:
#             print(f"\n=== 曲线数据预览 ===")
#             print(f"采样点数: {num_samples}")
#             print(f"归一化: {normalized}")
#             print(f"反转: {reversed}")
#             print(f"时间范围: {curve_data['time_range']}")
#             print(f"值范围: {curve_data['value_range']}")
#
#             # 显示前10个数据点
#             print("\n前10个数据点:")
#             print("时间     | 值")
#             print("-" * 20)
#             for i, (time, value) in enumerate(curve_data['curve_data'][:10]):
#                 print(f"{time:8.3f} | {value:8.3f}")
#
#             if len(curve_data['curve_data']) > 10:
#                 print(f"... (共{len(curve_data['curve_data'])}个点)")
#         else:
#             print("❌ 无法获取曲线数据")
#
#     def _connect_signals(self):
#         """连接信号"""
#         self.curve_widget.keyframe_changed.connect(self.on_keyframe_changed)
#         self.properties_widget.keyframe_property_changed.connect(self.on_property_changed)
#
#         # 设置快捷键
#         self.setup_shortcuts()
#
#     def on_property_changed(self):
#         """属性改变回调"""
#         # 属性编辑器已经更新了曲线，这里只需要发射信号
#         self.curve_widget.keyframe_changed.emit(self.curve_widget.keyframes)
#
#     def setup_shortcuts(self):
#         """设置快捷键"""
#         # Delete键删除关键帧
#         delete_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence.Delete, self)
#         delete_shortcut.activated.connect(self.delete_keyframe)
#
#         # Ctrl+A添加关键帧
#         add_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence("Ctrl+A"), self)
#         add_shortcut.activated.connect(self.add_keyframe)
#
#         # Esc取消选择
#         esc_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence(QtCore.Qt.Key_Escape), self)
#         esc_shortcut.activated.connect(self.clear_selection)
#
#         # Ctrl+Z撤销（预留）
#         undo_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence.Undo, self)
#         undo_shortcut.activated.connect(self.undo_action)
#
#         # Ctrl+Y重做（预留）
#         redo_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence.Redo, self)
#         redo_shortcut.activated.connect(self.redo_action)
#
#     def clear_selection(self):
#         """清除选择"""
#         self.curve_widget.selected_keyframe = None
#         self.curve_widget.update()
#         self.properties_widget.set_keyframe(None)
#         print("已取消选择")
#
#     def on_keyframe_changed(self, keyframes):
#         """关键帧改变回调"""
#         # 更新属性面板
#         if self.curve_widget.selected_keyframe:
#             self.properties_widget.set_keyframe(self.curve_widget.selected_keyframe)
#         else:
#             self.properties_widget.set_keyframe(None)
#
#         # 更新曲线显示
#         self.curve_widget.update()
#
#     def add_keyframe(self):
#         """添加关键帧"""
#         # 在时间轴中间添加关键帧
#         time_range = self.curve_widget.time_range
#         mid_time = (time_range[0] + time_range[1]) / 2
#         self.curve_widget.add_keyframe(mid_time, 0.0)
#
#     def delete_keyframe(self):
#         """删除选中的关键帧"""
#         if self.curve_widget.selected_keyframe:
#             self.curve_widget.remove_keyframe(self.curve_widget.selected_keyframe)
#
#     def update_time_range(self):
#         """更新时间范围"""
#         start = self.time_start_spinbox.value()
#         end = self.time_end_spinbox.value()
#         if start < end:
#             self.curve_widget.time_range = (start, end)
#             self.curve_widget.update()
#
#     def update_value_range(self):
#         """更新值范围"""
#         min_val = self.value_min_spinbox.value()
#         max_val = self.value_max_spinbox.value()
#         if min_val < max_val:
#             self.curve_widget.value_range = (min_val, max_val)
#             self.curve_widget.update()
#
#     def apply_linear_preset(self):
#         """应用线性预设"""
#         self.curve_widget.keyframes.clear()
#         self.curve_widget.add_keyframe(0.0, 0.0)
#         self.curve_widget.add_keyframe(10.0, 1.0)
#
#         # 设置线性切线
#         for kf in self.curve_widget.keyframes:
#             kf.in_tangent = (0.0, 0.0)
#             kf.out_tangent = (0.0, 0.0)
#
#     def apply_ease_in_preset(self):
#         """应用缓入预设"""
#         self.curve_widget.keyframes.clear()
#         self.curve_widget.add_keyframe(0.0, 0.0)
#         self.curve_widget.add_keyframe(10.0, 1.0)
#
#         self.curve_widget.keyframes[0].out_tangent = (3.0, 0.0)
#         self.curve_widget.keyframes[1].in_tangent = (-1.0, 0.0)
#
#     def apply_ease_out_preset(self):
#         """应用缓出预设"""
#         self.curve_widget.keyframes.clear()
#         self.curve_widget.add_keyframe(0.0, 0.0)
#         self.curve_widget.add_keyframe(10.0, 1.0)
#
#         self.curve_widget.keyframes[0].out_tangent = (1.0, 0.0)
#         self.curve_widget.keyframes[1].in_tangent = (-3.0, 0.0)
#
#     def apply_ease_in_out_preset(self):
#         """应用缓入缓出预设"""
#         self.curve_widget.keyframes.clear()
#         self.curve_widget.add_keyframe(0.0, 0.0)
#         self.curve_widget.add_keyframe(10.0, 1.0)
#
#         self.curve_widget.keyframes[0].out_tangent = (2.0, 0.0)
#         self.curve_widget.keyframes[1].in_tangent = (-2.0, 0.0)
#
#     def apply_bounce_preset(self):
#         """应用弹跳预设"""
#         self.curve_widget.keyframes.clear()
#         self.curve_widget.add_keyframe(0.0, 0.0)
#         self.curve_widget.add_keyframe(3.0, 1.2)
#         self.curve_widget.add_keyframe(6.0, 0.8)
#         self.curve_widget.add_keyframe(10.0, 1.0)
#
#     def reset_curve(self):
#         """重置曲线"""
#         # 保存状态
#         self.curve_widget.save_state("重置曲线")
#
#         self.curve_widget.keyframes.clear()
#         self.curve_widget.add_keyframe(0.0, 0.0)
#         self.curve_widget.add_keyframe(5.0, 1.0)
#         self.curve_widget.add_keyframe(10.0, 0.0)
#         self.curve_widget.update()
#         self.curve_widget.keyframe_changed.emit(self.curve_widget.keyframes)
#
#     def apply_tangent_preset(self):
#         """应用切线预设到当前选中的关键帧"""
#         if not self.curve_widget.selected_keyframe:
#             return
#
#         preset = self.tangent_preset_combo.currentText()
#         keyframe = self.curve_widget.selected_keyframe
#
#         # 保存状态
#         self.curve_widget.save_state(f"应用切线预设: {preset}")
#
#         if preset == "线性":
#             # 确保精确为0.0，避免浮点误差
#             keyframe.in_tangent = (0.0, 0.0)
#             keyframe.out_tangent = (0.0, 0.0)
#         elif preset == "缓入":
#             keyframe.in_tangent = (-2.0, 0.0)
#             keyframe.out_tangent = (1.0, 0.0)
#         elif preset == "缓出":
#             keyframe.in_tangent = (-1.0, 0.0)
#             keyframe.out_tangent = (2.0, 0.0)
#         elif preset == "缓入缓出":
#             keyframe.in_tangent = (-2.0, 0.0)
#             keyframe.out_tangent = (2.0, 0.0)
#         elif preset == "弹跳":
#             keyframe.in_tangent = (-1.5, -1.0)
#             keyframe.out_tangent = (1.5, 1.0)
#
#         # 更新属性面板和曲线
#         self.properties_widget.set_keyframe(keyframe)
#         self.curve_widget.update()
#         self.curve_widget.keyframe_changed.emit(self.curve_widget.keyframes)
#
#     def apply_preset_to_all_keyframes(self):
#         """将当前预设应用到所有关键帧"""
#         if not self.curve_widget.keyframes:
#             return
#
#         preset = self.tangent_preset_combo.currentText()
#
#         # 保存状态
#         self.curve_widget.save_state(f"应用切线预设到所有关键帧: {preset}")
#
#         for keyframe in self.curve_widget.keyframes:
#             if preset == "线性":
#                 keyframe.in_tangent = (0.0, 0.0)
#                 keyframe.out_tangent = (0.0, 0.0)
#             elif preset == "缓入":
#                 keyframe.in_tangent = (-2.0, 0.0)
#                 keyframe.out_tangent = (1.0, 0.0)
#             elif preset == "缓出":
#                 keyframe.in_tangent = (-1.0, 0.0)
#                 keyframe.out_tangent = (2.0, 0.0)
#             elif preset == "缓入缓出":
#                 keyframe.in_tangent = (-2.0, 0.0)
#                 keyframe.out_tangent = (2.0, 0.0)
#             elif preset == "弹跳":
#                 keyframe.in_tangent = (-1.5, -1.0)
#                 keyframe.out_tangent = (1.5, 1.0)
#
#         # 更新曲线
#         self.curve_widget.update()
#         self.curve_widget.keyframe_changed.emit(self.curve_widget.keyframes)
#
#     def get_curve_integration_data(self, num_samples=100, normalized=True, reversed=False):
#         """
#         获取用于眼动分析集成的曲线数据
#
#         Args:
#             num_samples (int): 采样点数量
#             normalized (bool): 是否归一化到0-1范围
#             reversed (bool): 是否反转曲线（1-0范围）
#
#         Returns:
#             dict: 曲线数据字典，可直接用于CurveEyeballIntegration
#         """
#         try:
#             from .utils.curve_eyeball_integration import CurveEyeballIntegration
#             integrator = CurveEyeballIntegration(self.curve_widget)
#             return integrator.extract_curve_data(num_samples, normalized, reversed)
#         except ImportError:
#             print("无法导入CurveEyeballIntegration模块")
#             return None
#
#     def export_curve_data_to_maya(self, object_name=None, attribute_name="scaleY",
#                                   num_samples=100, normalized=True, reversed=False):
#         """
#         将曲线数据直接导出到Maya对象
#
#         Args:
#             object_name (str): Maya对象名，如果为None则使用选中对象
#             attribute_name (str): 属性名
#             num_samples (int): 采样点数量
#             normalized (bool): 是否归一化
#             reversed (bool): 是否反转曲线
#
#         Returns:
#             bool: 是否成功导出
#         """
#         try:
#             from maya import cmds
#
#             # 获取对象名
#             if object_name is None:
#                 selected_objects = cmds.ls(selection=True)
#                 if not selected_objects:
#                     print("请选择一个对象或指定object_name参数")
#                     return False
#                 object_name = selected_objects[0]
#
#             # 获取曲线数据
#             curve_data = self.get_curve_integration_data(num_samples, normalized, reversed)
#             if not curve_data:
#                 print("无法获取曲线数据")
#                 return False
#
#             # 保存当前时间
#             current_time = cmds.currentTime(query=True)
#
#             try:
#                 # 应用曲线数据到对象
#                 curve_points = curve_data['curve_data']
#                 time_range = curve_data['time_range']
#
#                 # 计算帧范围
#                 start_frame = int(cmds.playbackOptions(query=True, minTime=True))
#                 end_frame = int(cmds.playbackOptions(query=True, maxTime=True))
#                 frame_count = end_frame - start_frame + 1
#
#                 print(f"将曲线数据应用到对象 '{object_name}' 的 '{attribute_name}' 属性")
#                 print(f"帧范围: {start_frame} - {end_frame} ({frame_count} 帧)")
#                 print(f"曲线设置: 采样={num_samples}, 归一化={normalized}, 反转={reversed}")
#
#                 for i, (norm_time, value) in enumerate(curve_points):
#                     # 将归一化时间映射到实际帧
#                     frame = start_frame + int(norm_time * (frame_count - 1))
#
#                     # 设置关键帧
#                     cmds.currentTime(frame)
#                     cmds.setAttr(f'{object_name}.{attribute_name}', value)
#                     cmds.setKeyframe(f'{object_name}.{attribute_name}')
#
#                 print(f"✅ 成功设置 {len(curve_points)} 个关键帧")
#                 return True
#
#             finally:
#                 # 恢复原始时间
#                 cmds.currentTime(current_time)
#
#         except ImportError:
#             print("此功能需要在Maya环境中运行")
#             return False
#         except Exception as e:
#             print(f"导出过程中出现错误: {e}")
#             return False
#
#     def create_eyeball_animation_preset(self):
#         """
#         创建眨眼动画预设曲线
#         """
#         # 保存状态
#         self.curve_widget.save_state("应用眨眼动画预设")
#
#         # 清除现有关键帧
#         self.curve_widget.keyframes.clear()
#
#         # 设置时间和值范围
#         self.curve_widget.time_range = (0.0, 10.0)
#         self.curve_widget.value_range = (0.0, 1.0)
#
#         # 创建眨眼曲线：睁眼(1) -> 闭眼(0) -> 睁眼(1)
#
#         keyframes = [
#             BezierPoint(time=0.0, value=1.0, in_tangent=(0.0, 0.0), out_tangent=(2.0, 0.0)),  # 睁眼
#             BezierPoint(time=3.0, value=0.0, in_tangent=(-2.0, 0.0), out_tangent=(2.0, 0.0)),  # 闭眼
#             BezierPoint(time=10.0, value=1.0, in_tangent=(-2.0, 0.0), out_tangent=(0.0, 0.0))  # 睁眼
#         ]
#
#         self.curve_widget.keyframes = keyframes
#
#         # 更新显示
#         self.curve_widget.update()
#         self.curve_widget.keyframe_changed.emit(self.curve_widget.keyframes)
#
#         print("✅ 已应用眨眼动画预设曲线")
#         print("  - 0.0秒: 睁眼 (值=1.0)")
#         print("  - 3.0秒: 闭眼 (值=0.0)")
#         print("  - 10.0秒: 睁眼 (值=1.0)")
