# -*- coding: utf-8 -*-

"""
测试贝塞尔曲线编辑器的拉伸功能

这个脚本用于验证BezierCurveWidget是否能正确跟随UI拉伸
"""

import sys
import os

# 添加路径以便导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from PySide2 import QtWidgets, QtCore, QtGui
except ImportError:
    try:
        from PyQt5 import QtWidgets, QtCore, QtGui
    except ImportError:
        print("需要安装PySide2或PyQt5")
        sys.exit(1)


def test_resize_functionality():
    """测试拉伸功能"""
    print("=== 测试贝塞尔曲线编辑器拉伸功能 ===")
    
    try:
        from bezier_curve_editor import BezierCurveEditor
        
        # 创建应用程序
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # 创建编辑器
        editor = BezierCurveEditor()
        
        print("✅ 编辑器创建成功")
        
        # 检查尺寸策略
        curve_widget = editor.curve_widget
        
        # 检查BezierCurveWidget的尺寸策略
        h_policy = curve_widget.sizePolicy().horizontalPolicy()
        v_policy = curve_widget.sizePolicy().verticalPolicy()
        
        print(f"BezierCurveWidget 水平策略: {h_policy}")
        print(f"BezierCurveWidget 垂直策略: {v_policy}")
        
        # 检查主编辑器的尺寸策略
        editor_h_policy = editor.sizePolicy().horizontalPolicy()
        editor_v_policy = editor.sizePolicy().verticalPolicy()
        
        print(f"BezierCurveEditor 水平策略: {editor_h_policy}")
        print(f"BezierCurveEditor 垂直策略: {editor_v_policy}")
        
        # 验证策略是否正确
        expanding_policy = QtWidgets.QSizePolicy.Expanding
        
        success = True
        if h_policy != expanding_policy:
            print("❌ BezierCurveWidget 水平策略不是 Expanding")
            success = False
        
        if v_policy != expanding_policy:
            print("❌ BezierCurveWidget 垂直策略不是 Expanding")
            success = False
        
        if editor_h_policy != expanding_policy:
            print("❌ BezierCurveEditor 水平策略不是 Expanding")
            success = False
        
        if editor_v_policy != expanding_policy:
            print("❌ BezierCurveEditor 垂直策略不是 Expanding")
            success = False
        
        if success:
            print("✅ 所有尺寸策略设置正确")
        
        # 测试实际拉伸
        print("\n--- 测试实际拉伸 ---")
        
        # 显示编辑器
        editor.show()
        
        # 获取初始尺寸
        initial_size = editor.size()
        initial_curve_size = curve_widget.size()
        
        print(f"初始编辑器尺寸: {initial_size.width()} x {initial_size.height()}")
        print(f"初始曲线控件尺寸: {initial_curve_size.width()} x {initial_curve_size.height()}")
        
        # 模拟拉伸
        new_width = initial_size.width() + 200
        new_height = initial_size.height() + 150
        editor.resize(new_width, new_height)
        
        # 强制更新布局
        app.processEvents()
        
        # 获取拉伸后的尺寸
        new_size = editor.size()
        new_curve_size = curve_widget.size()
        
        print(f"拉伸后编辑器尺寸: {new_size.width()} x {new_size.height()}")
        print(f"拉伸后曲线控件尺寸: {new_curve_size.width()} x {new_curve_size.height()}")
        
        # 验证拉伸效果
        width_increased = new_curve_size.width() > initial_curve_size.width()
        height_increased = new_curve_size.height() > initial_curve_size.height()
        
        if width_increased and height_increased:
            print("✅ 曲线控件成功跟随UI拉伸")
        else:
            if not width_increased:
                print("❌ 曲线控件宽度没有跟随拉伸")
            if not height_increased:
                print("❌ 曲线控件高度没有跟随拉伸")
        
        # 测试缩小
        print("\n--- 测试缩小 ---")
        
        smaller_width = initial_size.width() - 100
        smaller_height = initial_size.height() - 100
        editor.resize(smaller_width, smaller_height)
        
        app.processEvents()
        
        smaller_size = editor.size()
        smaller_curve_size = curve_widget.size()
        
        print(f"缩小后编辑器尺寸: {smaller_size.width()} x {smaller_size.height()}")
        print(f"缩小后曲线控件尺寸: {smaller_curve_size.width()} x {smaller_curve_size.height()}")
        
        # 验证缩小效果
        width_decreased = smaller_curve_size.width() < new_curve_size.width()
        height_decreased = smaller_curve_size.height() < new_curve_size.height()
        
        if width_decreased and height_decreased:
            print("✅ 曲线控件成功跟随UI缩小")
        else:
            if not width_decreased:
                print("❌ 曲线控件宽度没有跟随缩小")
            if not height_decreased:
                print("❌ 曲线控件高度没有跟随缩小")
        
        # 检查最小尺寸限制
        print("\n--- 测试最小尺寸限制 ---")
        
        min_size = curve_widget.minimumSize()
        print(f"曲线控件最小尺寸: {min_size.width()} x {min_size.height()}")
        
        # 尝试设置非常小的尺寸
        editor.resize(200, 150)
        app.processEvents()
        
        tiny_curve_size = curve_widget.size()
        print(f"极小尺寸时曲线控件尺寸: {tiny_curve_size.width()} x {tiny_curve_size.height()}")
        
        if tiny_curve_size.width() >= min_size.width() and tiny_curve_size.height() >= min_size.height():
            print("✅ 最小尺寸限制正常工作")
        else:
            print("❌ 最小尺寸限制可能有问题")
        
        print("\n=== 拉伸功能测试完成 ===")
        print("💡 提示: 可以手动拖拽窗口边缘来测试实际的拉伸效果")
        
        # 恢复到合适的尺寸
        editor.resize(1000, 700)
        
        return editor, app
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def create_test_window():
    """创建测试窗口"""
    print("\n=== 创建测试窗口 ===")
    
    try:
        from bezier_curve_editor import BezierCurveEditor
        
        # 创建应用程序
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QtWidgets.QMainWindow()
        main_window.setWindowTitle("贝塞尔曲线编辑器拉伸测试")
        main_window.setMinimumSize(600, 400)
        
        # 创建编辑器作为中央控件
        editor = BezierCurveEditor()
        main_window.setCentralWidget(editor)
        
        # 创建状态栏
        status_bar = main_window.statusBar()
        status_bar.showMessage("拖拽窗口边缘测试拉伸功能")
        
        # 创建菜单栏
        menu_bar = main_window.menuBar()
        test_menu = menu_bar.addMenu("测试")
        
        # 添加测试动作
        def test_large_size():
            main_window.resize(1200, 800)
            status_bar.showMessage("设置为大尺寸: 1200x800")
        
        def test_small_size():
            main_window.resize(800, 600)
            status_bar.showMessage("设置为小尺寸: 800x600")
        
        def test_minimum_size():
            main_window.resize(600, 400)
            status_bar.showMessage("设置为最小尺寸: 600x400")
        
        large_action = QtWidgets.QAction("大尺寸 (1200x800)", main_window)
        large_action.triggered.connect(test_large_size)
        test_menu.addAction(large_action)
        
        small_action = QtWidgets.QAction("小尺寸 (800x600)", main_window)
        small_action.triggered.connect(test_small_size)
        test_menu.addAction(small_action)
        
        min_action = QtWidgets.QAction("最小尺寸 (600x400)", main_window)
        min_action.triggered.connect(test_minimum_size)
        test_menu.addAction(min_action)
        
        # 显示窗口
        main_window.show()
        
        print("✅ 测试窗口创建成功")
        print("💡 使用菜单或手动拖拽来测试拉伸功能")
        
        return main_window, app
        
    except Exception as e:
        print(f"❌ 创建测试窗口失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def run_interactive_test():
    """运行交互式测试"""
    print("🚀 开始贝塞尔曲线编辑器拉伸功能测试")
    print("=" * 60)
    
    # 首先运行自动测试
    editor, app = test_resize_functionality()
    
    if editor and app:
        # 然后创建交互式测试窗口
        main_window, _ = create_test_window()
        
        if main_window:
            print("\n🎯 交互式测试说明:")
            print("1. 拖拽窗口边缘改变大小")
            print("2. 观察曲线编辑区域是否跟随拉伸")
            print("3. 使用菜单快速测试不同尺寸")
            print("4. 关闭窗口结束测试")
            
            # 运行应用程序
            try:
                app.exec_()
            except KeyboardInterrupt:
                print("\n测试被用户中断")
        
        print("\n✅ 拉伸功能测试完成")
    else:
        print("\n❌ 测试失败，无法创建编辑器")


if __name__ == "__main__":
    run_interactive_test()
