# -*- coding: utf-8 -*-

"""
测试对称切线功能

这个脚本用于验证入切线和出切线的对称功能是否正常工作
"""

import sys
import os

# 添加路径以便导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_bezier_point_symmetric_tangents():
    """测试BezierPoint的对称切线功能"""
    print("=== 测试BezierPoint对称切线功能 ===")
    
    try:
        from bezier_curve_editor import BezierPoint
        
        # 测试1：默认对称切线
        print("\n1. 测试默认对称切线:")
        kf = BezierPoint(time=5.0, value=1.0, out_tangent=(2.0, 1.0))
        print(f"  初始出切线: {kf.out_tangent}")
        print(f"  自动入切线: {kf.in_tangent}")
        print(f"  对称状态: {kf.symmetric_tangents}")
        
        # 验证是否对称
        expected_in = (-kf.out_tangent[0], -kf.out_tangent[1])
        if kf.in_tangent == expected_in:
            print("  ✅ 初始对称切线正确")
        else:
            print(f"  ❌ 初始对称切线错误，期望{expected_in}，实际{kf.in_tangent}")
        
        # 测试2：修改出切线，入切线自动跟随
        print("\n2. 测试修改出切线:")
        kf.out_tangent = (3.0, -1.5)
        print(f"  新出切线: {kf.out_tangent}")
        print(f"  自动入切线: {kf.in_tangent}")
        
        expected_in = (-3.0, 1.5)
        if kf.in_tangent == expected_in:
            print("  ✅ 出切线修改后入切线自动对称")
        else:
            print(f"  ❌ 出切线修改后入切线不对称，期望{expected_in}，实际{kf.in_tangent}")
        
        # 测试3：修改入切线，出切线自动跟随
        print("\n3. 测试修改入切线:")
        kf.in_tangent = (-1.0, 2.0)
        print(f"  新入切线: {kf.in_tangent}")
        print(f"  自动出切线: {kf.out_tangent}")
        
        expected_out = (1.0, -2.0)
        if kf.out_tangent == expected_out:
            print("  ✅ 入切线修改后出切线自动对称")
        else:
            print(f"  ❌ 入切线修改后出切线不对称，期望{expected_out}，实际{kf.out_tangent}")
        
        # 测试4：禁用对称切线
        print("\n4. 测试禁用对称切线:")
        kf.symmetric_tangents = False
        old_in = kf.in_tangent
        old_out = kf.out_tangent
        
        kf.out_tangent = (5.0, 0.0)
        print(f"  禁用对称后修改出切线: {kf.out_tangent}")
        print(f"  入切线是否保持不变: {kf.in_tangent == old_in}")
        
        if kf.in_tangent == old_in:
            print("  ✅ 禁用对称后切线独立工作")
        else:
            print("  ❌ 禁用对称后切线仍然联动")
        
        # 测试5：重新启用对称切线
        print("\n5. 测试重新启用对称切线:")
        kf.symmetric_tangents = True
        print(f"  重新启用后入切线: {kf.in_tangent}")
        print(f"  出切线: {kf.out_tangent}")
        
        expected_in = (-kf.out_tangent[0], -kf.out_tangent[1])
        if kf.in_tangent == expected_in:
            print("  ✅ 重新启用对称后切线同步正确")
        else:
            print(f"  ❌ 重新启用对称后切线同步错误")
        
        print("\n✅ BezierPoint对称切线功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ BezierPoint对称切线功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_curve_widget_symmetric_tangents():
    """测试曲线编辑器中的对称切线功能"""
    print("\n=== 测试曲线编辑器对称切线功能 ===")
    
    try:
        from bezier_curve_editor import BezierCurveWidget, BezierPoint
        
        # 创建曲线编辑器
        curve_widget = BezierCurveWidget()
        curve_widget.keyframes.clear()
        
        # 创建测试关键帧
        kf1 = BezierPoint(time=0.0, value=0.0)
        kf1.symmetric_tangents = True
        kf1.set_tangent_symmetric((2.0, 0.0), is_out_tangent=True)
        
        kf2 = BezierPoint(time=5.0, value=1.0)
        kf2.symmetric_tangents = True
        kf2.set_tangent_symmetric((1.5, 0.5), is_out_tangent=True)
        
        kf3 = BezierPoint(time=10.0, value=0.0)
        kf3.symmetric_tangents = True
        kf3.set_tangent_symmetric((2.0, 0.0), is_out_tangent=True)
        
        curve_widget.keyframes = [kf1, kf2, kf3]
        
        print("创建了3个对称切线关键帧")
        
        # 测试曲线计算
        print("\n测试曲线计算:")
        test_times = [0.0, 2.5, 5.0, 7.5, 10.0]
        
        for t in test_times:
            value = curve_widget.evaluate_curve(t)
            print(f"  时间 {t:4.1f}: 值 {value:.3f}")
        
        # 验证对称性对曲线的影响
        print("\n验证对称切线对曲线的影响:")
        
        # 获取中点关键帧的切线
        mid_kf = kf2
        print(f"中点关键帧 (时间={mid_kf.time}):")
        print(f"  入切线: {mid_kf.in_tangent}")
        print(f"  出切线: {mid_kf.out_tangent}")
        print(f"  对称状态: {mid_kf.symmetric_tangents}")
        
        # 检查对称性
        expected_in = (-mid_kf.out_tangent[0], -mid_kf.out_tangent[1])
        if mid_kf.in_tangent == expected_in:
            print("  ✅ 中点关键帧切线对称正确")
        else:
            print("  ❌ 中点关键帧切线不对称")
        
        # 测试曲线采样
        print("\n测试曲线采样:")
        curve_data = curve_widget.get_curve_data(num_samples=11)
        
        if curve_data:
            print("  采样成功，前5个点:")
            for i, (time, value) in enumerate(curve_data[:5]):
                print(f"    点{i}: 时间={time:.2f}, 值={value:.3f}")
            print("  ✅ 对称切线不影响曲线采样")
        else:
            print("  ❌ 曲线采样失败")
        
        print("\n✅ 曲线编辑器对称切线功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 曲线编辑器对称切线功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_preset_tangent_modes():
    """测试预设切线模式"""
    print("\n=== 测试预设切线模式 ===")
    
    try:
        from bezier_curve_editor import BezierPoint
        
        # 测试不同预设的对称性设置
        presets = {
            "自由": True,      # 应该启用对称
            "线性": False,     # 应该禁用对称
            "缓入": False,     # 应该禁用对称
            "缓出": False,     # 应该禁用对称
            "缓入缓出": True,  # 应该启用对称
            "弹跳": False      # 应该禁用对称
        }
        
        for preset_name, should_be_symmetric in presets.items():
            print(f"\n测试预设: {preset_name}")
            
            kf = BezierPoint(time=5.0, value=1.0)
            
            # 模拟预设应用逻辑
            if preset_name == "自由":
                kf.symmetric_tangents = True
                kf._sync_tangents_from_out()
            elif preset_name == "线性":
                kf.symmetric_tangents = False
                kf.set_tangents_directly((0.0, 0.0), (0.0, 0.0))
            elif preset_name == "缓入":
                kf.symmetric_tangents = False
                kf.set_tangents_directly((-2.0, 0.0), (1.0, 0.0))
            elif preset_name == "缓出":
                kf.symmetric_tangents = False
                kf.set_tangents_directly((-1.0, 0.0), (2.0, 0.0))
            elif preset_name == "缓入缓出":
                kf.symmetric_tangents = True
                kf.set_tangent_symmetric((2.0, 0.0), is_out_tangent=True)
            elif preset_name == "弹跳":
                kf.symmetric_tangents = False
                kf.set_tangents_directly((-1.5, -1.0), (1.5, 1.0))
            
            print(f"  对称状态: {kf.symmetric_tangents} (期望: {should_be_symmetric})")
            print(f"  入切线: {kf.in_tangent}")
            print(f"  出切线: {kf.out_tangent}")
            
            if kf.symmetric_tangents == should_be_symmetric:
                print(f"  ✅ {preset_name} 预设对称性设置正确")
            else:
                print(f"  ❌ {preset_name} 预设对称性设置错误")
            
            # 对于对称预设，验证切线是否真的对称
            if should_be_symmetric and kf.symmetric_tangents:
                expected_in = (-kf.out_tangent[0], -kf.out_tangent[1])
                if kf.in_tangent == expected_in:
                    print(f"  ✅ {preset_name} 切线对称正确")
                else:
                    print(f"  ❌ {preset_name} 切线不对称")
        
        print("\n✅ 预设切线模式测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 预设切线模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有对称切线测试"""
    print("🚀 开始对称切线功能测试")
    print("=" * 60)
    
    tests = [
        ("BezierPoint对称切线", test_bezier_point_symmetric_tangents),
        ("曲线编辑器对称切线", test_curve_widget_symmetric_tangents),
        ("预设切线模式", test_preset_tangent_modes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有对称切线测试通过！")
        print("\n💡 主要功能:")
        print("  - 入切线和出切线永远对称")
        print("  - 调整入切线时自动调整出切线")
        print("  - 调整出切线时自动调整入切线")
        print("  - 可以通过UI切换对称模式")
        print("  - 不同预设有不同的对称性设置")
    else:
        print("⚠️  部分测试失败，请检查对称切线功能")


if __name__ == "__main__":
    run_all_tests()
