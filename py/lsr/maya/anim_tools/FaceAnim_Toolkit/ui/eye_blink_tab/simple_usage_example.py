# -*- coding: utf-8 -*-

"""
简化版贝塞尔曲线编辑器使用示例

展示如何使用简化后的编辑器进行曲线采样、归一化和反转
"""

import sys
import os

# 添加路径以便导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    from bezier_curve_editor import BezierCurveWidget, BezierPoint
    
    # 1. 创建曲线编辑器
    curve_widget = BezierCurveWidget()
    curve_widget.keyframes.clear()
    
    # 2. 设置曲线范围
    curve_widget.time_range = (0.0, 10.0)
    curve_widget.value_range = (0.0, 1.0)
    
    # 3. 创建眨眼曲线：睁眼(1) -> 闭眼(0) -> 睁眼(1)
    keyframes = [
        BezierPoint(time=0.0, value=1.0, in_tangent=(0.0, 0.0), out_tangent=(2.0, 0.0)),    # 睁眼
        BezierPoint(time=3.0, value=0.0, in_tangent=(-2.0, 0.0), out_tangent=(2.0, 0.0)),  # 闭眼
        BezierPoint(time=10.0, value=1.0, in_tangent=(-2.0, 0.0), out_tangent=(0.0, 0.0))  # 睁眼
    ]
    curve_widget.keyframes = keyframes
    
    print("创建了眨眼曲线，包含3个关键帧")
    
    # 4. 获取不同类型的曲线数据
    print("\n--- 曲线数据提取 ---")
    
    # 原始数据
    original_data = curve_widget.get_curve_data(num_samples=20)
    print(f"原始数据: {len(original_data)} 个点")
    print(f"时间范围: {original_data[0][0]:.1f} - {original_data[-1][0]:.1f}")
    print(f"值范围: {min(p[1] for p in original_data):.3f} - {max(p[1] for p in original_data):.3f}")
    
    # 归一化数据
    normalized_data = curve_widget.get_normalized_curve_data(num_samples=20)
    print(f"\n归一化数据: {len(normalized_data)} 个点")
    print(f"时间范围: {normalized_data[0][0]:.1f} - {normalized_data[-1][0]:.1f}")
    print(f"值范围: {min(p[1] for p in normalized_data):.3f} - {max(p[1] for p in normalized_data):.3f}")
    
    # 反转数据
    reversed_data = curve_widget.get_reversed_curve_data(num_samples=20)
    print(f"\n反转数据: {len(reversed_data)} 个点")
    print(f"时间范围: {reversed_data[0][0]:.1f} - {reversed_data[-1][0]:.1f}")
    print(f"值范围: {min(p[1] for p in reversed_data):.3f} - {max(p[1] for p in reversed_data):.3f}")
    
    # 仅值数组
    values_normal = curve_widget.get_curve_values_only(num_samples=10, normalized=True, reversed=False)
    values_reversed = curve_widget.get_curve_values_only(num_samples=10, normalized=True, reversed=True)
    
    print(f"\n归一化值数组: {[f'{v:.2f}' for v in values_normal]}")
    print(f"反转值数组: {[f'{v:.2f}' for v in values_reversed]}")
    
    return curve_widget


def example_different_curves():
    """不同类型曲线的示例"""
    print("\n=== 不同类型曲线示例 ===")
    
    from bezier_curve_editor import BezierCurveWidget, BezierPoint
    
    curves = {
        "线性曲线": [
            BezierPoint(time=0.0, value=0.0, in_tangent=(0.0, 0.0), out_tangent=(0.0, 0.0)),
            BezierPoint(time=5.0, value=1.0, in_tangent=(0.0, 0.0), out_tangent=(0.0, 0.0)),
            BezierPoint(time=10.0, value=0.0, in_tangent=(0.0, 0.0), out_tangent=(0.0, 0.0))
        ],
        "缓入缓出曲线": [
            BezierPoint(time=0.0, value=0.0, in_tangent=(0.0, 0.0), out_tangent=(2.0, 0.0)),
            BezierPoint(time=5.0, value=1.0, in_tangent=(-2.0, 0.0), out_tangent=(2.0, 0.0)),
            BezierPoint(time=10.0, value=0.0, in_tangent=(-2.0, 0.0), out_tangent=(0.0, 0.0))
        ],
        "弹跳曲线": [
            BezierPoint(time=0.0, value=0.0, in_tangent=(0.0, 0.0), out_tangent=(1.0, 0.0)),
            BezierPoint(time=3.0, value=1.2, in_tangent=(-1.0, 1.0), out_tangent=(1.0, -1.0)),
            BezierPoint(time=7.0, value=0.8, in_tangent=(-1.0, 0.5), out_tangent=(1.0, -0.5)),
            BezierPoint(time=10.0, value=1.0, in_tangent=(-1.0, 0.0), out_tangent=(0.0, 0.0))
        ]
    }
    
    for curve_name, keyframes in curves.items():
        print(f"\n--- {curve_name} ---")
        
        # 创建曲线
        curve_widget = BezierCurveWidget()
        curve_widget.keyframes.clear()
        curve_widget.time_range = (0.0, 10.0)
        curve_widget.value_range = (0.0, 1.2)
        curve_widget.keyframes = keyframes
        
        # 获取归一化值数组
        values = curve_widget.get_curve_values_only(num_samples=11, normalized=True, reversed=False)
        print(f"归一化值: {[f'{v:.2f}' for v in values]}")
        
        # 获取反转值数组
        reversed_values = curve_widget.get_curve_values_only(num_samples=11, normalized=True, reversed=True)
        print(f"反转值: {[f'{v:.2f}' for v in reversed_values]}")


def example_practical_usage():
    """实际应用示例"""
    print("\n=== 实际应用示例 ===")
    
    from bezier_curve_editor import BezierCurveWidget, BezierPoint
    
    # 创建一个复杂的眨眼动画曲线
    curve_widget = BezierCurveWidget()
    curve_widget.keyframes.clear()
    curve_widget.time_range = (0.0, 20.0)
    curve_widget.value_range = (0.0, 1.0)
    
    # 复杂眨眼：正常 -> 快速闭眼 -> 微睁 -> 慢速睁眼
    keyframes = [
        BezierPoint(time=0.0, value=1.0, in_tangent=(0.0, 0.0), out_tangent=(1.0, 0.0)),    # 正常睁眼
        BezierPoint(time=2.0, value=0.0, in_tangent=(-3.0, 0.0), out_tangent=(0.5, 0.0)),  # 快速闭眼
        BezierPoint(time=8.0, value=0.1, in_tangent=(-0.5, 0.0), out_tangent=(1.0, 0.0)),  # 微睁
        BezierPoint(time=20.0, value=1.0, in_tangent=(-2.0, 0.0), out_tangent=(0.0, 0.0))  # 慢速睁眼
    ]
    curve_widget.keyframes = keyframes
    
    print("创建了复杂眨眼动画曲线")
    
    # 不同精度的采样
    precisions = [10, 50, 100]
    
    for precision in precisions:
        values = curve_widget.get_curve_values_only(num_samples=precision, normalized=True, reversed=False)
        print(f"\n{precision}点采样 - 前5个值: {[f'{v:.3f}' for v in values[:5]]}")
        print(f"{precision}点采样 - 后5个值: {[f'{v:.3f}' for v in values[-5:]]}")
    
    # 应用场景示例
    print("\n--- 应用场景 ---")
    
    # 场景1: 眼皮缩放动画
    print("场景1: 眼皮缩放动画 (scaleY)")
    scale_values = curve_widget.get_curve_values_only(num_samples=30, normalized=True, reversed=False)
    print(f"缩放值范围: {min(scale_values):.3f} - {max(scale_values):.3f}")
    
    # 场景2: 眼皮旋转动画 (反转曲线)
    print("\n场景2: 眼皮旋转动画 (rotateX, 反转)")
    rotation_values = curve_widget.get_curve_values_only(num_samples=30, normalized=True, reversed=True)
    print(f"旋转值范围: {min(rotation_values):.3f} - {max(rotation_values):.3f}")
    
    # 场景3: 可见性动画 (二值化)
    print("\n场景3: 可见性动画 (visibility, 二值化)")
    visibility_values = curve_widget.get_curve_values_only(num_samples=30, normalized=True, reversed=False)
    # 将连续值转换为0/1
    binary_values = [1 if v > 0.5 else 0 for v in visibility_values]
    print(f"二值化结果: {binary_values}")
    
    return curve_widget


def example_ui_usage():
    """UI使用示例"""
    print("\n=== UI使用示例 ===")
    
    try:
        from bezier_curve_editor import BezierCurveEditor
        
        print("创建贝塞尔曲线编辑器UI...")
        
        # 创建编辑器
        editor = BezierCurveEditor()
        
        print("✅ 编辑器创建成功")
        print("\n可用功能:")
        print("1. 曲线编辑: 在左侧区域点击创建关键帧，拖拽调整")
        print("2. 切线编辑: 选中关键帧后拖拽黄色切线控制点")
        print("3. 预设曲线: 使用右侧预设按钮快速创建曲线")
        print("4. 曲线采样: 配置采样参数后点击'预览曲线数据'")
        print("5. 快捷键: Delete删除, Ctrl+Z撤销, Ctrl+Y重做")
        
        # 如果在支持GUI的环境中，可以显示编辑器
        # editor.show()
        
        return editor
        
    except Exception as e:
        print(f"❌ UI创建失败: {e}")
        return None


def run_all_examples():
    """运行所有示例"""
    print("🚀 简化版贝塞尔曲线编辑器使用示例")
    print("=" * 60)
    
    examples = [
        ("基本使用", example_basic_usage),
        ("不同类型曲线", example_different_curves),
        ("实际应用", example_practical_usage),
        ("UI使用", example_ui_usage)
    ]
    
    results = {}
    
    for example_name, example_func in examples:
        print(f"\n📋 运行示例: {example_name}")
        try:
            result = example_func()
            results[example_name] = result
            print(f"✅ {example_name} 示例完成")
        except Exception as e:
            print(f"❌ {example_name} 示例失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("📊 示例总结")
    print("✅ 主要功能已简化并保留:")
    print("  - 曲线采样: get_curve_data()")
    print("  - 归一化: get_normalized_curve_data()")
    print("  - 反转: get_reversed_curve_data()")
    print("  - 值数组: get_curve_values_only()")
    print("✅ UI已简化:")
    print("  - 移除了时间范围和值范围控制")
    print("  - 移除了关键帧属性编辑栏")
    print("  - 保留了曲线采样功能")
    print("✅ 入切线正确影响曲线形状")


if __name__ == "__main__":
    run_all_examples()
