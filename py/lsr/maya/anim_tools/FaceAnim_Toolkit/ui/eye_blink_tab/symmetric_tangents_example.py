# -*- coding: utf-8 -*-

"""
对称切线功能使用示例

展示如何使用新的对称切线功能来创建平滑的贝塞尔曲线
"""

import sys
import os

# 添加路径以便导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def example_basic_symmetric_tangents():
    """基本对称切线使用示例"""
    print("=== 基本对称切线使用示例 ===")
    
    from bezier_curve_editor import BezierPoint
    
    # 1. 创建启用对称切线的关键帧
    print("\n1. 创建对称切线关键帧:")
    kf = BezierPoint(time=5.0, value=1.0)
    print(f"  默认对称状态: {kf.symmetric_tangents}")
    
    # 2. 设置出切线，入切线自动对称
    print("\n2. 设置出切线，观察入切线自动对称:")
    kf.out_tangent = (2.0, 1.0)
    print(f"  设置出切线: {kf.out_tangent}")
    print(f"  自动入切线: {kf.in_tangent}")
    
    # 3. 设置入切线，出切线自动对称
    print("\n3. 设置入切线，观察出切线自动对称:")
    kf.in_tangent = (-1.5, 0.5)
    print(f"  设置入切线: {kf.in_tangent}")
    print(f"  自动出切线: {kf.out_tangent}")
    
    # 4. 禁用对称，独立设置切线
    print("\n4. 禁用对称，独立设置切线:")
    kf.symmetric_tangents = False
    kf.in_tangent = (-3.0, 1.0)
    kf.out_tangent = (1.0, -2.0)
    print(f"  独立入切线: {kf.in_tangent}")
    print(f"  独立出切线: {kf.out_tangent}")
    
    # 5. 重新启用对称
    print("\n5. 重新启用对称:")
    kf.symmetric_tangents = True
    print(f"  重新启用后入切线: {kf.in_tangent}")
    print(f"  出切线: {kf.out_tangent}")
    
    return kf


def example_smooth_curve_creation():
    """创建平滑曲线示例"""
    print("\n=== 创建平滑曲线示例 ===")
    
    from bezier_curve_editor import BezierCurveWidget, BezierPoint
    
    # 创建曲线编辑器
    curve_widget = BezierCurveWidget()
    curve_widget.keyframes.clear()
    curve_widget.time_range = (0.0, 10.0)
    curve_widget.value_range = (0.0, 1.0)
    
    print("\n创建平滑的S型曲线:")
    
    # 起点：不对称，平缓开始
    kf1 = BezierPoint(time=0.0, value=0.0)
    kf1.symmetric_tangents = False
    kf1.set_tangents_directly((0.0, 0.0), (2.0, 0.0))
    print(f"起点 - 对称: {kf1.symmetric_tangents}, 入切线: {kf1.in_tangent}, 出切线: {kf1.out_tangent}")
    
    # 中点1：对称，平滑过渡
    kf2 = BezierPoint(time=3.0, value=0.3)
    kf2.symmetric_tangents = True
    kf2.set_tangent_symmetric((1.5, 0.5), is_out_tangent=True)
    print(f"中点1 - 对称: {kf2.symmetric_tangents}, 入切线: {kf2.in_tangent}, 出切线: {kf2.out_tangent}")
    
    # 中点2：对称，平滑过渡
    kf3 = BezierPoint(time=7.0, value=0.7)
    kf3.symmetric_tangents = True
    kf3.set_tangent_symmetric((1.5, 0.2), is_out_tangent=True)
    print(f"中点2 - 对称: {kf3.symmetric_tangents}, 入切线: {kf3.in_tangent}, 出切线: {kf3.out_tangent}")
    
    # 终点：不对称，平缓结束
    kf4 = BezierPoint(time=10.0, value=1.0)
    kf4.symmetric_tangents = False
    kf4.set_tangents_directly((-2.0, 0.0), (0.0, 0.0))
    print(f"终点 - 对称: {kf4.symmetric_tangents}, 入切线: {kf4.in_tangent}, 出切线: {kf4.out_tangent}")
    
    curve_widget.keyframes = [kf1, kf2, kf3, kf4]
    
    # 采样曲线
    print("\n曲线采样结果:")
    curve_data = curve_widget.get_curve_data(num_samples=11)
    for i, (time, value) in enumerate(curve_data):
        print(f"  点{i:2d}: 时间={time:5.2f}, 值={value:.3f}")
    
    return curve_widget


def example_animation_curves():
    """动画曲线示例"""
    print("\n=== 动画曲线示例 ===")
    
    from bezier_curve_editor import BezierCurveWidget, BezierPoint
    
    # 示例1：弹跳动画（非对称）
    print("\n1. 弹跳动画曲线（非对称切线）:")
    bounce_curve = BezierCurveWidget()
    bounce_curve.keyframes.clear()
    
    # 弹跳动画需要非对称切线来创建尖锐的峰值
    kf1 = BezierPoint(time=0.0, value=0.0)
    kf1.symmetric_tangents = False
    kf1.set_tangents_directly((0.0, 0.0), (1.0, 0.0))
    
    kf2 = BezierPoint(time=2.0, value=1.0)
    kf2.symmetric_tangents = False  # 非对称创建尖锐峰值
    kf2.set_tangents_directly((-0.5, 2.0), (0.5, -2.0))
    
    kf3 = BezierPoint(time=4.0, value=0.2)
    kf3.symmetric_tangents = False
    kf3.set_tangents_directly((-0.5, 0.5), (0.5, -0.5))
    
    kf4 = BezierPoint(time=6.0, value=0.0)
    kf4.symmetric_tangents = False
    kf4.set_tangents_directly((-1.0, 0.0), (0.0, 0.0))
    
    bounce_curve.keyframes = [kf1, kf2, kf3, kf4]
    
    print("  弹跳曲线关键帧:")
    for i, kf in enumerate(bounce_curve.keyframes):
        print(f"    关键帧{i}: 时间={kf.time}, 值={kf.value:.1f}, 对称={kf.symmetric_tangents}")
    
    # 示例2：平滑眨眼动画（混合对称）
    print("\n2. 平滑眨眼动画曲线（混合对称）:")
    blink_curve = BezierCurveWidget()
    blink_curve.keyframes.clear()
    
    # 睁眼状态：不对称起点
    kf1 = BezierPoint(time=0.0, value=1.0)
    kf1.symmetric_tangents = False
    kf1.set_tangents_directly((0.0, 0.0), (1.5, 0.0))
    
    # 闭眼状态：对称中点，创建平滑过渡
    kf2 = BezierPoint(time=3.0, value=0.0)
    kf2.symmetric_tangents = True
    kf2.set_tangent_symmetric((1.0, 0.0), is_out_tangent=True)
    
    # 睁眼状态：不对称终点
    kf3 = BezierPoint(time=8.0, value=1.0)
    kf3.symmetric_tangents = False
    kf3.set_tangents_directly((-1.5, 0.0), (0.0, 0.0))
    
    blink_curve.keyframes = [kf1, kf2, kf3]
    
    print("  眨眼曲线关键帧:")
    for i, kf in enumerate(blink_curve.keyframes):
        print(f"    关键帧{i}: 时间={kf.time}, 值={kf.value:.1f}, 对称={kf.symmetric_tangents}")
    
    # 比较两种曲线的采样
    print("\n3. 曲线采样对比:")
    bounce_data = bounce_curve.get_curve_values_only(num_samples=7, normalized=False, reversed=False)
    blink_data = blink_curve.get_curve_values_only(num_samples=7, normalized=False, reversed=False)
    
    print("  采样点 | 弹跳曲线 | 眨眼曲线")
    print("  -------|----------|----------")
    for i in range(7):
        bounce_val = bounce_data[i] if i < len(bounce_data) else 0.0
        blink_val = blink_data[i] if i < len(blink_data) else 0.0
        print(f"    {i:2d}   |  {bounce_val:6.3f}  |  {blink_val:6.3f}")
    
    return bounce_curve, blink_curve


def example_ui_usage():
    """UI使用示例"""
    print("\n=== UI使用示例 ===")
    
    try:
        from bezier_curve_editor import BezierCurveEditor
        
        print("创建贝塞尔曲线编辑器...")
        editor = BezierCurveEditor()
        
        print("✅ 编辑器创建成功")
        print("\n对称切线UI功能:")
        print("1. 选择关键帧后，右侧面板显示'对称切线'复选框")
        print("2. 勾选'对称切线'启用对称模式")
        print("3. 取消勾选禁用对称模式")
        print("4. '同步选中关键帧'按钮应用当前设置到选中关键帧")
        print("5. 拖拽切线控制点时自动保持对称")
        
        print("\n切线预设说明:")
        print("- 自由: 启用对称切线")
        print("- 线性: 禁用对称，零切线")
        print("- 缓入/缓出: 禁用对称，非对称切线")
        print("- 缓入缓出: 启用对称，对称切线")
        print("- 弹跳: 禁用对称，非对称切线")
        
        print("\n操作步骤:")
        print("1. 点击曲线区域创建关键帧")
        print("2. 选中关键帧，观察对称切线复选框状态")
        print("3. 拖拽黄色切线控制点，观察对称效果")
        print("4. 切换对称切线复选框，观察行为变化")
        print("5. 使用不同预设，观察对称性设置")
        
        return editor
        
    except Exception as e:
        print(f"❌ UI创建失败: {e}")
        return None


def run_all_examples():
    """运行所有示例"""
    print("🚀 对称切线功能使用示例")
    print("=" * 60)
    
    examples = [
        ("基本对称切线", example_basic_symmetric_tangents),
        ("平滑曲线创建", example_smooth_curve_creation),
        ("动画曲线", example_animation_curves),
        ("UI使用", example_ui_usage)
    ]
    
    results = {}
    
    for example_name, example_func in examples:
        print(f"\n📋 运行示例: {example_name}")
        try:
            result = example_func()
            results[example_name] = result
            print(f"✅ {example_name} 示例完成")
        except Exception as e:
            print(f"❌ {example_name} 示例失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("📊 对称切线功能总结")
    print("✅ 主要特性:")
    print("  - 入切线和出切线永远对称")
    print("  - 调整任一切线时另一个自动跟随")
    print("  - 可以通过UI或代码切换对称模式")
    print("  - 不同预设有合适的对称性设置")
    print("  - 支持混合使用对称和非对称关键帧")
    
    print("\n✅ 使用场景:")
    print("  - 对称切线: 平滑过渡、自然动画")
    print("  - 非对称切线: 尖锐变化、特殊效果")
    print("  - 混合使用: 复杂动画曲线")
    
    print("\n💡 最佳实践:")
    print("  - 中间关键帧使用对称切线保持平滑")
    print("  - 起始和结束关键帧可使用非对称切线")
    print("  - 根据动画需求选择合适的预设")


if __name__ == "__main__":
    run_all_examples()
