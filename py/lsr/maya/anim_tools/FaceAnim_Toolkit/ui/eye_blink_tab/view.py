# -*- coding: utf-8 -*-

"""
眼动分析工具UI
"""


from Qt import QtWidgets, QtCore


class KeyframePropertiesWidget(QtWidgets.QWidget):
    """关键帧属性编辑器"""

    keyframe_property_changed = QtCore.Signal()  # 属性改变信号

    def __init__(self, parent=None):
        super(KeyframePropertiesWidget, self).__init__(parent)
        self.current_keyframe = None
        self.curve_widget = None  # 引用曲线编辑器
        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        layout = QtWidgets.QFormLayout()

        # 时间输入
        self.time_spinbox = QtWidgets.QDoubleSpinBox()
        self.time_spinbox.setRange(-999.0, 999.0)
        self.time_spinbox.setDecimals(3)
        self.time_spinbox.valueChanged.connect(self.on_time_changed)
        layout.addRow("时间:", self.time_spinbox)

        # 值输入
        self.value_spinbox = QtWidgets.QDoubleSpinBox()
        self.value_spinbox.setRange(-999.0, 999.0)
        self.value_spinbox.setDecimals(3)
        self.value_spinbox.valueChanged.connect(self.on_value_changed)
        layout.addRow("值:", self.value_spinbox)

        # 入切线
        self.in_tangent_x = QtWidgets.QDoubleSpinBox()
        self.in_tangent_x.setRange(-10.0, 10.0)
        self.in_tangent_x.setDecimals(3)
        self.in_tangent_x.valueChanged.connect(self.on_in_tangent_changed)

        self.in_tangent_y = QtWidgets.QDoubleSpinBox()
        self.in_tangent_y.setRange(-10.0, 10.0)
        self.in_tangent_y.setDecimals(3)
        self.in_tangent_y.valueChanged.connect(self.on_in_tangent_changed)

        in_layout = QtWidgets.QHBoxLayout()
        in_layout.addWidget(QtWidgets.QLabel("X:"))
        in_layout.addWidget(self.in_tangent_x)
        in_layout.addWidget(QtWidgets.QLabel("Y:"))
        in_layout.addWidget(self.in_tangent_y)

        in_widget = QtWidgets.QWidget()
        in_widget.setLayout(in_layout)
        layout.addRow("入切线:", in_widget)

        # 出切线
        self.out_tangent_x = QtWidgets.QDoubleSpinBox()
        self.out_tangent_x.setRange(-10.0, 10.0)
        self.out_tangent_x.setDecimals(3)
        self.out_tangent_x.valueChanged.connect(self.on_out_tangent_changed)

        self.out_tangent_y = QtWidgets.QDoubleSpinBox()
        self.out_tangent_y.setRange(-10.0, 10.0)
        self.out_tangent_y.setDecimals(3)
        self.out_tangent_y.valueChanged.connect(self.on_out_tangent_changed)

        out_layout = QtWidgets.QHBoxLayout()
        out_layout.addWidget(QtWidgets.QLabel("X:"))
        out_layout.addWidget(self.out_tangent_x)
        out_layout.addWidget(QtWidgets.QLabel("Y:"))
        out_layout.addWidget(self.out_tangent_y)

        out_widget = QtWidgets.QWidget()
        out_widget.setLayout(out_layout)
        layout.addRow("出切线:", out_widget)

        self.setLayout(layout)
        self.setEnabled(False)

    def set_keyframe(self, keyframe):
        """设置当前编辑的关键帧"""
        self.current_keyframe = keyframe

        if keyframe:
            self.setEnabled(True)
            self.time_spinbox.blockSignals(True)
            self.value_spinbox.blockSignals(True)
            self.in_tangent_x.blockSignals(True)
            self.in_tangent_y.blockSignals(True)
            self.out_tangent_x.blockSignals(True)
            self.out_tangent_y.blockSignals(True)

            self.time_spinbox.setValue(keyframe.time)
            self.value_spinbox.setValue(keyframe.value)
            self.in_tangent_x.setValue(keyframe.in_tangent[0])
            self.in_tangent_y.setValue(keyframe.in_tangent[1])
            self.out_tangent_x.setValue(keyframe.out_tangent[0])
            self.out_tangent_y.setValue(keyframe.out_tangent[1])

            self.time_spinbox.blockSignals(False)
            self.value_spinbox.blockSignals(False)
            self.in_tangent_x.blockSignals(False)
            self.in_tangent_y.blockSignals(False)
            self.out_tangent_x.blockSignals(False)
            self.out_tangent_y.blockSignals(False)
        else:
            self.setEnabled(False)

    def on_time_changed(self):
        """时间改变"""
        if self.current_keyframe and self.curve_widget:
            old_time = self.current_keyframe.time
            new_time = self.time_spinbox.value()

            if old_time != new_time:
                # 保存状态
                self.curve_widget.save_state(f"修改时间 {old_time:.3f} -> {new_time:.3f}")

                self.current_keyframe.time = new_time
                # 重新排序关键帧
                self.curve_widget.keyframes.sort(key=lambda k: k.time)
                self.curve_widget.update()
                self.keyframe_property_changed.emit()

    def on_value_changed(self):
        """值改变"""
        if self.current_keyframe and self.curve_widget:
            old_value = self.current_keyframe.value
            new_value = self.value_spinbox.value()

            if old_value != new_value:
                # 保存状态
                self.curve_widget.save_state(f"修改值 {old_value:.3f} -> {new_value:.3f}")

                self.current_keyframe.value = new_value
                self.curve_widget.update()
                self.keyframe_property_changed.emit()

    def on_in_tangent_changed(self):
        """入切线改变"""
        if self.current_keyframe and self.curve_widget:
            old_tangent = self.current_keyframe.in_tangent
            new_tangent = (self.in_tangent_x.value(), self.in_tangent_y.value())

            if old_tangent != new_tangent:
                # 保存状态
                self.curve_widget.save_state(f"修改入切线")

                self.current_keyframe.in_tangent = new_tangent
                self.curve_widget.update()
                self.keyframe_property_changed.emit()

    def on_out_tangent_changed(self):
        """出切线改变"""
        if self.current_keyframe and self.curve_widget:
            old_tangent = self.current_keyframe.out_tangent
            new_tangent = (self.out_tangent_x.value(), self.out_tangent_y.value())

            if old_tangent != new_tangent:
                # 保存状态
                self.curve_widget.save_state(f"修改出切线")

                self.current_keyframe.out_tangent = new_tangent
                self.curve_widget.update()
                self.keyframe_property_changed.emit()

    def set_curve_widget(self, curve_widget):
        """设置曲线编辑器引用"""
        self.curve_widget = curve_widget