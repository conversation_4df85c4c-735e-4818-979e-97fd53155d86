# -*- coding: utf-8 -*-

"""
批量Maya文件引用路径替换工具
"""

import os
import shutil
from pathlib import Path
from maya import cmds
from maya import mel


class BatchReferenceReplacer:
    """批量Maya文件引用替换器"""

    def __init__(self):
        self.old_reference_path = "J:/Content/OG2/IMMOArts/Characters/Player/Player/Common/EmptyHand/Maya/player_actor_rig.ma"
        self.new_reference_path = "J:/Content/OG2/IMMOArts/Characters/Player_new/Player/Common/EmptyHand/Maya/player_actor_rig.ma"
        self.processed_files = []
        self.error_files = []
        # HumanIK 相关的关节名称
        self.target_joints = ['LeftHand', 'RightHand', 'LeftForeArm', 'RightForeArm']

    def find_maya_files(self, directory):
        """
        递归查找目录下所有Maya文件

        Args:
            directory (str): 要搜索的目录路径

        Returns:
            list: Maya文件路径列表
        """
        maya_files = []
        directory = Path(directory)

        if not directory.exists():
            print(f"错误: 目录不存在 - {directory}")
            return maya_files

        # 递归查找所有.ma和.mb文件
        for file_path in directory.rglob("*.ma"):
            maya_files.append(str(file_path))
        for file_path in directory.rglob("*.mb"):
            maya_files.append(str(file_path))

        print(f"在目录 '{directory}' 中找到 {len(maya_files)} 个Maya文件")
        return maya_files

    def check_reference_in_file(self, file_path):
        """
        检查Maya文件中是否包含指定的引用路径

        Args:
            file_path (str): Maya文件路径

        Returns:
            bool: 如果包含指定引用返回True，否则返回False
        """
        try:
            # 打开Maya文件
            cmds.file(file_path, open=True, force=True, ignoreVersion=True)

            # 获取所有引用节点
            ref_nodes = cmds.ls(type="reference")

            for ref_node in ref_nodes:
                try:
                    # 获取引用文件路径
                    ref_file_path = cmds.referenceQuery(ref_node, filename=True)
                    print(ref_file_path)
                    print(self.old_reference_path)

                    # 标准化路径进行比较
                    ref_file_path = os.path.normpath(ref_file_path)
                    old_path = os.path.normpath(self.old_reference_path)

                    if ref_file_path == old_path:
                        print(f"找到匹配的引用: {ref_file_path}")
                        return True

                except Exception as e:
                    print(f"警告: 无法查询引用节点 {ref_node}: {e}")
                    continue

            return False

        except Exception as e:
            print(f"错误: 无法打开文件 {file_path}: {e}")
            return False

    def replace_reference_in_file(self, file_path):
        """
        替换Maya文件中的引用路径

        Args:
            file_path (str): Maya文件路径

        Returns:
            bool: 替换成功返回True，否则返回False
        """
        try:
            # 打开Maya文件
            cmds.file(file_path, open=True, force=True, ignoreVersion=True)

            # 获取所有引用节点
            ref_nodes = cmds.ls(type="reference")
            replaced = False

            for ref_node in ref_nodes:
                try:
                    # 获取引用文件路径
                    ref_file_path = cmds.referenceQuery(ref_node, filename=True)

                    # 标准化路径进行比较
                    ref_file_path = ref_file_path.replace("\\", "/")
                    old_path = self.old_reference_path.replace("\\", "/")

                    if ref_file_path == old_path:
                        # 替换引用
                        print(f"替换引用: {old_path} -> {self.new_reference_path}")
                        cmds.file(self.new_reference_path, loadReference=ref_node)
                        replaced = True

                except Exception as e:
                    print(f"警告: 无法处理引用节点 {ref_node}: {e}")
                    continue

            return replaced

        except Exception as e:
            print(f"错误: 无法处理文件 {file_path}: {e}")
            return False

    def get_current_hik_character(self):
        """
        获取当前场景中的 HumanIK 角色节点

        Returns:
            str: HumanIK 角色节点名称，如果没有找到返回 None
        """
        try:
            # 获取所有 HIKCharacterNode
            hik_nodes = cmds.ls(type='HIKCharacterNode')
            if not hik_nodes:
                print("警告: 场景中没有找到 HumanIK 角色")
                return None

            for hik_node in hik_nodes:
                if 'OG2_Male_0002' in hik_node:
                    return hik_node
                else:
                    print(f"警告: HumanIK 节点不存在: {hik_node}")

            return None

        except Exception as e:
            print(f"错误: 获取 HumanIK 角色失败: {e}")
            return None

    def get_joint_from_hik_character(self, hik_node, joint_name):
        """
        从 HumanIK 角色获取指定关节

        Args:
            hik_node (str): HumanIK 角色节点名称
            joint_name (str): 关节名称 (如 'LeftHand', 'RightHand' 等)

        Returns:
            str: 关节节点名称，如果没有找到返回 None
        """
        try:
            if not cmds.objExists(hik_node):
                print(f"错误: HumanIK 节点不存在: {hik_node}")
                return None

            # 检查是否有该关节属性
            if not cmds.attributeQuery(joint_name, node=hik_node, exists=True):
                print(f"警告: HumanIK 角色 {hik_node} 没有 {joint_name} 属性")
                return None

            # 获取连接到该属性的关节
            connections = cmds.listConnections(f"{hik_node}.{joint_name}", source=True, destination=False)
            if connections:
                joint = connections[0]
                if cmds.objExists(joint) and cmds.objectType(joint) == 'joint':
                    return joint
                else:
                    print(f"警告: 连接的对象不是关节: {joint}")
                    return None
            else:
                print(f"警告: HumanIK 角色 {hik_node} 的 {joint_name} 没有连接关节")
                return None

        except Exception as e:
            print(f"错误: 获取关节 {joint_name} 失败: {e}")
            return None

    def create_locator_for_joint(self, joint, locator_name):
        """
        为关节创建 locator 并约束

        Args:
            joint (str): 关节名称
            locator_name (str): locator 名称

        Returns:
            str: 创建的 locator 名称，失败返回 None
        """
        try:
            if not cmds.objExists(joint):
                print(f"错误: 关节不存在: {joint}")
                return None

            # 创建 locator
            locator = cmds.spaceLocator(name=locator_name)[0]

            # 约束 locator 到关节
            constraint = cmds.parentConstraint(joint, locator, maintainOffset=False)[0]

            print(f"为关节 {joint} 创建了 locator: {locator}")
            return locator, constraint

        except Exception as e:
            print(f"错误: 为关节 {joint} 创建 locator 失败: {e}")
            return None

    def bake_locator_animation(self, locators_and_constraints):
        """
        烘焙 locator 动画

        Args:
            locators_and_constraints (list): [(locator, constraint), ...] 列表

        Returns:
            bool: 烘焙成功返回 True，否则返回 False
        """
        try:
            if not locators_and_constraints:
                print("警告: 没有 locator 需要烘焙")
                return False

            # 获取时间范围
            start_time = cmds.playbackOptions(query=True, minTime=True)
            end_time = cmds.playbackOptions(query=True, maxTime=True)

            # 提取所有 locator
            locators = [item[0] for item in locators_and_constraints]

            # 烘焙动画
            cmds.bakeResults(
                locators,
                simulation=True,
                time=(start_time, end_time),
                sampleBy=1,
                oversamplingRate=1,
                disableImplicitControl=True,
                preserveOutsideKeys=True,
                sparseAnimCurveBake=False,
                removeBakedAttributeFromLayer=False,
                removeBakedAnimFromLayer=False,
                bakeOnOverrideLayer=False,
                minimizeRotation=True,
                controlPoints=False,
                shape=False
            )

            # 删除约束
            constraints = [item[1] for item in locators_and_constraints]
            cmds.delete(constraints)

            print(f"成功烘焙了 {len(locators)} 个 locator 的动画")
            return True

        except Exception as e:
            print(f"错误: 烘焙 locator 动画失败: {e}")
            return False

    def create_output_directory(self, input_directory):
        """
        创建输出目录

        Args:
            input_directory (str): 输入目录路径

        Returns:
            str: 输出目录路径
        """
        input_path = Path(input_directory)
        output_path = Path(str(input_path) + "_new")

        # 如果输出目录不存在，创建它
        if not output_path.exists():
            output_path.mkdir(parents=True, exist_ok=True)
            print(f"创建输出目录: {output_path}")

        return str(output_path)

    def get_output_file_path(self, input_file_path, input_directory, output_directory):
        """
        获取输出文件路径

        Args:
            input_file_path (str): 输入文件路径
            input_directory (str): 输入目录路径
            output_directory (str): 输出目录路径

        Returns:
            str: 输出文件路径
        """
        input_path = Path(input_file_path)
        input_dir = Path(input_directory)
        output_dir = Path(output_directory)

        # 计算相对路径
        relative_path = input_path.relative_to(input_dir)

        # 构建输出路径
        output_path = output_dir / relative_path

        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)

        return str(output_path)

    def bake_and_save_hik_joints(self, input_file_path):
        """
        烘焙 HumanIK 关节动画并保存到 _old 文件

        Args:
            input_file_path (str): 输入文件路径

        Returns:
            bool: 成功返回 True，否则返回 False
        """
        try:
            print("开始烘焙 HumanIK 关节动画...")

            # 获取当前 HumanIK 角色
            hik_node = self.get_current_hik_character()
            if not hik_node:
                print("跳过烘焙：没有找到 HumanIK 角色")
                return True  # 不是错误，只是跳过

            # 获取目标关节
            joints_dict = {}
            for joint_name in self.target_joints:
                joint = self.get_joint_from_hik_character(hik_node, joint_name)
                if joint:
                    joints_dict[joint_name] = joint
                else:
                    print(f"警告: 未找到关节 {joint_name}")

            if not joints_dict:
                print("跳过烘焙：没有找到任何目标关节")
                return True  # 不是错误，只是跳过

            # 为关节创建 locator
            locators_and_constraints = []
            for joint_name, joint in joints_dict.items():
                locator_name = f"{joint_name}_baked_loc"
                result = self.create_locator_for_joint(joint, locator_name)
                if result:
                    locators_and_constraints.append(result)

            if not locators_and_constraints:
                print("错误: 无法创建任何 locator")
                return False

            # 烘焙动画
            if not self.bake_locator_animation(locators_and_constraints):
                print("错误: 烘焙动画失败")
                return False

            # 计算保存路径
            input_path = Path(input_file_path)
            old_file_path = str(input_path.parent / (input_path.stem + "_old" + input_path.suffix))

            # 只保存这4个烘焙后的 locator
            locators = [item[0] for item in locators_and_constraints]
            if self.save_locators_only(locators, old_file_path):
                print(f"成功保存烘焙的 locator 到: {old_file_path}")

                # 清理 locator
                cmds.delete(locators)
                print("已清理临时 locator")

                return True
            else:
                print(f"错误: 保存烘焙的 locator 失败: {old_file_path}")
                return False

        except Exception as e:
            print(f"错误: 烘焙和保存 HumanIK 关节动画失败: {e}")
            return False

    def save_locators_only(self, locators, output_path):
        """
        只保存指定的 locator 到文件

        Args:
            locators (list): locator 列表
            output_path (str): 输出文件路径

        Returns:
            bool: 保存成功返回 True，否则返回 False
        """
        try:
            if not locators:
                print("错误: 没有 locator 需要保存")
                return False

            # 检查 locator 是否存在
            existing_locators = []
            for loc in locators:
                if cmds.objExists(loc):
                    existing_locators.append(loc)
                else:
                    print(f"警告: locator 不存在: {loc}")

            if not existing_locators:
                print("错误: 没有有效的 locator 可以保存")
                return False

            # 选择所有有效的 locator
            cmds.select(existing_locators, replace=True)

            # 确定文件类型
            ext = Path(output_path).suffix.lower()
            if ext == '.ma':
                file_type = 'mayaAscii'
            elif ext == '.mb':
                file_type = 'mayaBinary'
            else:
                print(f"错误: 不支持的文件类型 {ext}")
                return False

            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 导出选中的 locator
            cmds.file(
                output_path,
                exportSelected=True,
                type=file_type,
                force=True
            )

            print(f"成功导出 {len(existing_locators)} 个 locator 到: {output_path}")
            return True

        except Exception as e:
            print(f"错误: 保存 locator 失败: {e}")
            return False

    def save_file(self, output_path):
        """
        保存Maya文件

        Args:
            output_path (str): 输出文件路径

        Returns:
            bool: 保存成功返回True，否则返回False
        """
        try:
            # 确定文件类型
            ext = Path(output_path).suffix.lower()
            if ext == '.ma':
                file_type = 'mayaAscii'
            elif ext == '.mb':
                file_type = 'mayaBinary'
            else:
                print(f"错误: 不支持的文件类型 {ext}")
                return False

            # 重命名并保存文件
            cmds.file(rename=output_path)
            cmds.file(save=True, type=file_type, force=True)
            print(f"文件已保存: {output_path}")
            return True

        except Exception as e:
            print(f"错误: 无法保存文件 {output_path}: {e}")
            return False

    def process_directory(self, input_directory):
        """
        处理整个目录

        Args:
            input_directory (str): 输入目录路径

        Returns:
            dict: 处理结果统计
        """
        print(f"开始处理目录: {input_directory}")

        # 查找所有Maya文件
        maya_files = self.find_maya_files(input_directory)

        if not maya_files:
            print("未找到Maya文件")
            return {"total": 0, "processed": 0, "errors": 0}

        # 创建输出目录
        output_directory = self.create_output_directory(input_directory)

        # 重置统计
        self.processed_files = []
        self.error_files = []

        # 处理每个文件
        for i, file_path in enumerate(maya_files, 1):
            print(f"\n处理文件 {i}/{len(maya_files)}: {file_path}")

            try:
                # 检查文件是否包含目标引用
                if self.check_reference_in_file(file_path):
                    print("文件包含目标引用，开始替换...")

                    # 先烘焙并保存 HumanIK 关节动画到 _old 文件
                    if not self.bake_and_save_hik_joints(file_path):
                        print("警告: 烘焙 HumanIK 关节动画失败，但继续处理")

                    # 替换引用
                    if self.replace_reference_in_file(file_path):

                        # 计算输出路径
                        output_path = self.get_output_file_path(
                            file_path, input_directory, output_directory
                        )

                        self.fix_ik_anim(locator_path=file_path.replace(".ma", "_old.ma"))

                        # 保存文件
                        if self.save_file(output_path):
                            self.processed_files.append((file_path, output_path))
                            print("文件处理完成")
                        else:
                            self.error_files.append(file_path)
                    else:
                        print("引用替换失败")
                        self.error_files.append(file_path)
                else:
                    print("文件不包含目标引用，跳过")

            except Exception as e:
                print(f"处理文件时出错: {e}")
                self.error_files.append(file_path)

        # 创建新场景，清理内存
        cmds.file(new=True, force=True)

        # 返回统计结果
        result = {
            "total": len(maya_files),
            "processed": len(self.processed_files),
            "errors": len(self.error_files)
        }

        self.print_summary(result)
        return result

    def fix_ik_anim(self, locator_path):
        """
        修复 HumanIK 动画

        Args:
            locator_path (str): locator 文件路径

        Returns:
            bool: 修复成功返回 True，否则返回 False
        """
        try:
            print("开始修复 HumanIK 动画...")

            # 导入 locator 文件
            if not os.path.exists(locator_path):
                print(f"警告: locator 文件不存在: {locator_path}")
                return False

            cmds.file(locator_path, i=True, force=True, ignoreVersion=True)
            print(f"成功导入 locator 文件: {locator_path}")

            # 定义 locator 到控制器的映射关系
            locator_ctrl_mapping = {
                'RightHand_baked_loc': 'arm_ik_R_CTRL',
                'LeftHand_baked_loc': 'arm_ik_L_CTRL',
                'RightForeArm_baked_loc': 'arm_upv_R_CTRL',
                'LeftForeArm_baked_loc': 'arm_upv_L_CTRL'
            }

            constraints_created = []

            # 为每个 locator 创建约束
            for locator_name, ctrl_suffix in locator_ctrl_mapping.items():
                if not cmds.objExists(locator_name):
                    print(f"警告: locator 不存在: {locator_name}")
                    continue

                # 查找所有以指定后缀结尾的控制器
                all_objects = cmds.ls()
                matching_ctrls = [obj for obj in all_objects if obj.endswith(ctrl_suffix)]

                if not matching_ctrls:
                    print(f"警告: 没有找到以 {ctrl_suffix} 结尾的控制器")
                    continue

                print(f"为 {locator_name} 找到 {len(matching_ctrls)} 个匹配的控制器: {matching_ctrls}")

                # 为每个匹配的控制器创建约束
                for ctrl in matching_ctrls:
                    if cmds.objExists(ctrl):
                        try:
                            # 创建父约束
                            constraint = cmds.parentConstraint(locator_name, ctrl, maintainOffset=False)[0]
                            constraints_created.append(constraint)
                            print(f"创建约束: {locator_name} -> {ctrl}")
                        except Exception as e:
                            print(f"警告: 无法为 {ctrl} 创建约束: {e}")
                    else:
                        print(f"警告: 控制器不存在: {ctrl}")

            # 设置 arm_root 控制器的 blend 属性为 1
            arm_root_suffixes = ['arm_root_L_CTRL', 'arm_root_R_CTRL']
            arm_root_ctrls = []

            for suffix in arm_root_suffixes:
                all_objects = cmds.ls()
                matching_roots = [obj for obj in all_objects if obj.endswith(suffix)]
                arm_root_ctrls.extend(matching_roots)

            if arm_root_ctrls:
                print(f"找到 {len(arm_root_ctrls)} 个 arm_root 控制器: {arm_root_ctrls}")

                # 获取时间范围
                start_time = cmds.playbackOptions(query=True, minTime=True)
                end_time = cmds.playbackOptions(query=True, maxTime=True)

                # 为每个 arm_root 控制器设置 blend 属性
                blend_objects = []
                for ctrl in arm_root_ctrls:
                    if cmds.objExists(ctrl) and cmds.attributeQuery('blend', node=ctrl, exists=True):
                        # 设置 blend 属性为 1
                        cmds.setAttr(f"{ctrl}.blend", 1)
                        # 在整个时间范围内设置关键帧
                        cmds.setKeyframe(f"{ctrl}.blend", time=start_time, value=1)
                        cmds.setKeyframe(f"{ctrl}.blend", time=end_time, value=1)
                        blend_objects.append(f"{ctrl}.blend")
                        print(f"设置 {ctrl}.blend = 1")
                    else:
                        print(f"警告: {ctrl} 没有 blend 属性或对象不存在")

                # 烘焙 blend 属性
                if blend_objects:
                    try:
                        cmds.bakeResults(
                            blend_objects,
                            simulation=True,
                            time=(start_time, end_time),
                            sampleBy=1,
                            oversamplingRate=1,
                            disableImplicitControl=True,
                            preserveOutsideKeys=True,
                            sparseAnimCurveBake=False,
                            removeBakedAttributeFromLayer=False,
                            removeBakedAnimFromLayer=False,
                            bakeOnOverrideLayer=False,
                            minimizeRotation=True,
                            controlPoints=False,
                            shape=False
                        )
                        print(f"成功烘焙 {len(blend_objects)} 个 blend 属性")
                    except Exception as e:
                        print(f"警告: 烘焙 blend 属性失败: {e}")
            else:
                print("警告: 没有找到 arm_root 控制器")

            # 烘焙所有约束的控制器
            if constraints_created:
                # 获取所有被约束的对象
                constrained_objects = []
                for constraint in constraints_created:
                    if cmds.objExists(constraint):
                        # 获取约束的目标对象
                        targets = cmds.parentConstraint(constraint, query=True, targetList=True)
                        if targets:
                            constrained_objects.extend(targets)

                # 去重
                constrained_objects = list(set(constrained_objects))

                if constrained_objects:
                    try:
                        # 烘焙约束的对象
                        cmds.bakeResults(
                            constrained_objects,
                            simulation=True,
                            time=(start_time, end_time),
                            sampleBy=1,
                            oversamplingRate=1,
                            disableImplicitControl=True,
                            preserveOutsideKeys=True,
                            sparseAnimCurveBake=False,
                            removeBakedAttributeFromLayer=False,
                            removeBakedAnimFromLayer=False,
                            bakeOnOverrideLayer=False,
                            minimizeRotation=True,
                            controlPoints=False,
                            shape=False
                        )
                        print(f"成功烘焙 {len(constrained_objects)} 个约束对象")

                        # 删除约束
                        cmds.delete(constraints_created)
                        print("已删除临时约束")

                    except Exception as e:
                        print(f"警告: 烘焙约束对象失败: {e}")

            # 删除导入的 locator
            imported_locators = ['RightHand_baked_loc', 'LeftHand_baked_loc', 'RightForeArm_baked_loc', 'LeftForeArm_baked_loc']
            for locator in imported_locators:
                if cmds.objExists(locator):
                    cmds.delete(locator)
                    print(f"删除导入的 locator: {locator}")

            os.remove(locator_path)
            print("HumanIK 动画修复完成")
            return True

        except Exception as e:
            print(f"错误: 修复 HumanIK 动画失败: {e}")
            return False


    def print_summary(self, result):
        """
        打印处理摘要

        Args:
            result (dict): 处理结果统计
        """
        print("\n" + "=" * 50)
        print("处理摘要")
        print("=" * 50)
        print(f"总文件数: {result['total']}")
        print(f"成功处理: {result['processed']}")
        print(f"处理失败: {result['errors']}")

        if self.processed_files:
            print("\n成功处理的文件:")
            for input_file, output_file in self.processed_files:
                print(f"  {input_file} -> {output_file}")

        if self.error_files:
            print("\n处理失败的文件:")
            for error_file in self.error_files:
                print(f"  {error_file}")


def batch_replace_references(directory_path):
    """
    批量替换引用的便捷函数

    Args:
        directory_path (str): 要处理的目录路径

    Returns:
        dict: 处理结果统计
    """
    replacer = BatchReferenceReplacer()
    return replacer.process_directory(directory_path)


input_dir = "E:/local_test_folder/OG2_IKFix/001"  # 替换为您的实际路径

try:
    result = batch_replace_references(input_dir)
    print(f"\n处理完成! 成功处理了 {result['processed']} 个文件")
except Exception as e:
    print(f"批处理过程中出现错误: {e}")
    import traceback

    traceback.print_exc()
