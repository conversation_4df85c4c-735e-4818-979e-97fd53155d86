# -*- coding: utf-8 -*-

"""
贝塞尔曲线与眼动分析集成测试脚本

这个脚本用于测试curve_eyeball_integration模块的功能
"""

import sys
import os

# 添加路径以便导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_curve_data_extraction():
    """测试曲线数据提取功能"""
    print("=== 测试曲线数据提取 ===")
    
    try:
        from ..bezier_curve_editor import BezierCurveWidget, BezierPoint
        from .curve_eyeball_integration import CurveEyeballIntegration
        
        # 创建曲线编辑器
        curve_widget = BezierCurveWidget()
        curve_widget.keyframes.clear()
        
        # 创建测试曲线
        curve_widget.time_range = (0.0, 10.0)
        curve_widget.value_range = (0.0, 1.0)
        
        keyframes = [
            BezierPoint(time=0.0, value=1.0, in_tangent=(0.0, 0.0), out_tangent=(2.0, 0.0)),
            BezierPoint(time=5.0, value=0.0, in_tangent=(-2.0, 0.0), out_tangent=(2.0, 0.0)),
            BezierPoint(time=10.0, value=1.0, in_tangent=(-2.0, 0.0), out_tangent=(0.0, 0.0))
        ]
        curve_widget.keyframes = keyframes
        
        # 创建集成器
        integrator = CurveEyeballIntegration(curve_widget)
        
        # 测试不同类型的数据提取
        print("\n1. 原始数据:")
        original_data = integrator.extract_curve_data(num_samples=10, normalized=False, reversed=False)
        if original_data:
            print(f"  时间范围: {original_data['time_range']}")
            print(f"  值范围: {original_data['value_range']}")
            print(f"  前3个点: {original_data['curve_data'][:3]}")
        
        print("\n2. 归一化数据:")
        normalized_data = integrator.extract_curve_data(num_samples=10, normalized=True, reversed=False)
        if normalized_data:
            print(f"  时间范围: {normalized_data['time_range']}")
            print(f"  值范围: {normalized_data['value_range']}")
            print(f"  前3个点: {normalized_data['curve_data'][:3]}")
        
        print("\n3. 反转数据:")
        reversed_data = integrator.extract_curve_data(num_samples=10, normalized=True, reversed=True)
        if reversed_data:
            print(f"  时间范围: {reversed_data['time_range']}")
            print(f"  值范围: {reversed_data['value_range']}")
            print(f"  前3个点: {reversed_data['curve_data'][:3]}")
        
        print("✅ 曲线数据提取测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 曲线数据提取测试失败: {e}")
        return False


def test_eyeball_analysis():
    """测试眼动分析功能"""
    print("\n=== 测试眼动分析 ===")
    
    try:
        from .analysis_eyeball_rotation import AnalysisEyeballRotation
        from maya import OpenMaya
        import math
        
        # 创建测试矩阵数据
        frame_matrix_dict = {}
        
        for frame in range(1, 16):  # 15帧数据
            if 5 <= frame <= 8:  # 在第5-8帧模拟眨眼
                angle = math.radians(30 + (frame - 5) * 10)
                cos_a = math.cos(angle)
                sin_a = math.sin(angle)
                
                matrix = OpenMaya.MMatrix([
                    1, 0, 0, 0,
                    0, cos_a, -sin_a, 0,
                    0, sin_a, cos_a, 0,
                    0, 0, 0, 1
                ])
            else:
                matrix = OpenMaya.MMatrix([
                    1, 0, 0, 0,
                    0, 1, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1
                ])
            
            frame_matrix_dict[frame] = matrix
        
        # 创建分析器
        analyzer = AnalysisEyeballRotation()
        analyzer.frame_matrix_dict = frame_matrix_dict
        analyzer.frame_spacing = 2
        analyzer.angle_threshold = 15
        
        # 执行分析
        result = analyzer.analyze_rotation_changes()
        
        print(f"检测到 {len(result)} 个眨眼标记:")
        for frame in sorted(result.keys()):
            marker_type = "眨眼开始/完成" if result[frame] == 0 else "眨眼结束"
            print(f"  帧 {frame}: {result[frame]} ({marker_type})")
        
        print("✅ 眼动分析测试通过")
        return result
        
    except Exception as e:
        print(f"❌ 眼动分析测试失败: {e}")
        return None


def test_integration():
    """测试完整集成功能"""
    print("\n=== 测试完整集成 ===")
    
    try:
        from ..bezier_curve_editor import BezierCurveWidget, BezierPoint
        from .curve_eyeball_integration import CurveEyeballIntegration
        from .analysis_eyeball_rotation import AnalysisEyeballRotation
        from maya import OpenMaya
        import math
        
        # 创建曲线编辑器
        curve_widget = BezierCurveWidget()
        curve_widget.keyframes.clear()
        curve_widget.time_range = (0.0, 10.0)
        curve_widget.value_range = (0.0, 1.0)
        
        keyframes = [
            BezierPoint(time=0.0, value=1.0, in_tangent=(0.0, 0.0), out_tangent=(2.0, 0.0)),
            BezierPoint(time=3.0, value=0.0, in_tangent=(-2.0, 0.0), out_tangent=(2.0, 0.0)),
            BezierPoint(time=10.0, value=1.0, in_tangent=(-2.0, 0.0), out_tangent=(0.0, 0.0))
        ]
        curve_widget.keyframes = keyframes
        
        # 创建测试矩阵数据
        frame_matrix_dict = {}
        for frame in range(1, 21):
            if 5 <= frame <= 8:
                angle = math.radians(30 + (frame - 5) * 10)
                cos_a = math.cos(angle)
                sin_a = math.sin(angle)
                matrix = OpenMaya.MMatrix([
                    1, 0, 0, 0,
                    0, cos_a, -sin_a, 0,
                    0, sin_a, cos_a, 0,
                    0, 0, 0, 1
                ])
            else:
                matrix = OpenMaya.MMatrix([
                    1, 0, 0, 0,
                    0, 1, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1
                ])
            frame_matrix_dict[frame] = matrix
        
        # 创建集成器
        integrator = CurveEyeballIntegration(curve_widget)
        
        # 模拟完整流程（不实际在Maya中创建关键帧）
        analyzer = AnalysisEyeballRotation()
        analyzer.frame_matrix_dict = frame_matrix_dict
        analyzer.frame_spacing = 2
        analyzer.angle_threshold = 15
        
        analysis_result = analyzer.analyze_rotation_changes()
        curve_data = integrator.extract_curve_data(num_samples=50, normalized=True, reversed=False)
        
        print(f"眼动分析结果: {len(analysis_result)} 个标记")
        print(f"曲线数据: {len(curve_data['values_only'])} 个采样点")
        
        # 模拟应用过程
        print("\n模拟应用曲线到眨眼标记:")
        frames_sorted = sorted(analysis_result.items())
        for frame, marker in frames_sorted:
            if marker == 0:
                curve_value = curve_data['values_only'][0]  # 开始值
                print(f"  帧 {frame}: 眨眼开始，曲线值 = {curve_value:.3f}")
            else:
                curve_value = curve_data['values_only'][-1]  # 结束值
                print(f"  帧 {frame}: 眨眼结束，曲线值 = {curve_value:.3f}")
        
        print("✅ 完整集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 完整集成测试失败: {e}")
        return False


def test_curve_widget_methods():
    """测试曲线编辑器的新方法"""
    print("\n=== 测试曲线编辑器新方法 ===")
    
    try:
        from ..bezier_curve_editor import BezierCurveWidget, BezierPoint
        
        # 创建曲线编辑器
        curve_widget = BezierCurveWidget()
        curve_widget.keyframes.clear()
        curve_widget.time_range = (0.0, 10.0)
        curve_widget.value_range = (0.0, 1.0)
        
        keyframes = [
            BezierPoint(time=0.0, value=1.0, in_tangent=(0.0, 0.0), out_tangent=(1.0, 0.0)),
            BezierPoint(time=5.0, value=0.0, in_tangent=(-1.0, 0.0), out_tangent=(1.0, 0.0)),
            BezierPoint(time=10.0, value=1.0, in_tangent=(-1.0, 0.0), out_tangent=(0.0, 0.0))
        ]
        curve_widget.keyframes = keyframes
        
        # 测试新的数据提取方法
        print("1. 测试 get_curve_data:")
        curve_data = curve_widget.get_curve_data(num_samples=5)
        print(f"  数据点数: {len(curve_data)}")
        print(f"  前3个点: {curve_data[:3]}")
        
        print("\n2. 测试 get_normalized_curve_data:")
        normalized_data = curve_widget.get_normalized_curve_data(num_samples=5)
        print(f"  数据点数: {len(normalized_data)}")
        print(f"  前3个点: {normalized_data[:3]}")
        
        print("\n3. 测试 get_reversed_curve_data:")
        reversed_data = curve_widget.get_reversed_curve_data(num_samples=5)
        print(f"  数据点数: {len(reversed_data)}")
        print(f"  前3个点: {reversed_data[:3]}")
        
        print("\n4. 测试 get_curve_values_only:")
        values_only = curve_widget.get_curve_values_only(num_samples=5, normalized=True, reversed=False)
        print(f"  值数组: {values_only}")
        
        reversed_values = curve_widget.get_curve_values_only(num_samples=5, normalized=True, reversed=True)
        print(f"  反转值数组: {reversed_values}")
        
        print("✅ 曲线编辑器新方法测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 曲线编辑器新方法测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行贝塞尔曲线与眼动分析集成测试")
    print("=" * 60)
    
    tests = [
        ("曲线编辑器新方法", test_curve_widget_methods),
        ("曲线数据提取", test_curve_data_extraction),
        ("眼动分析", test_eyeball_analysis),
        ("完整集成", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！集成功能正常工作")
    else:
        print("⚠️  部分测试失败，请检查相关模块")


if __name__ == "__main__":
    run_all_tests()
