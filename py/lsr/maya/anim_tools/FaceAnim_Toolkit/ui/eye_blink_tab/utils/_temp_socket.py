
from maya import cmds

mapping = {
    'Hand_R_Limb': ['IkHandGun_M_Limb', 'IkHand_R_Limb', 'itemIkChestToWorld_R_Limb', 'WeaponSlot_R_Limb', 'weaponSlotToWorld_R_Limb'],
    'Hand_L_Limb': ['IkHand_L_Limb', 'itemIkChestToWorld_L_Limb', 'WeaponSlot_L_Limb', 'weaponSlotToWorld_L_Limb'],
    'foot_R_Limb': ['IkFoot_R_Limb'],
    'foot_L_Limb': ['IkFoot_L_Limb'],
    'itemHand_L_Limb': ['magLoaded_L_Limb', 'magReload_L_Limb', 'itemSlotChestToWorld_L_Limb'],
    'itemHand_R_Limb': ['magLoaded_R_Limb', 'magReload_R_Limb', 'itemSlotChestToWorld_R_Limb'],
    'arm_01_R_RIGJNT': ['IkArmPole_R_Limb'],
    'arm_01_L_RIGJNT': ['IkArmPole_L_Limb']
}

for src, dst in mapping.items():
    if cmds.objExists(src):
        for d in dst:
            if cmds.objExists(d):
                src_mat = cmds.xform(src, query=True, worldSpace=True, matrix=True)
                cmds.xform(d, worldSpace=True, matrix=src_mat)
