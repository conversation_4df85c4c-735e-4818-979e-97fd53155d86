# -*- coding: utf-8 -*-

"""
贝塞尔曲线与眼动分析集成使用示例

这个文件展示了如何使用curve_eyeball_integration模块来：
1. 从贝塞尔曲线编辑器提取曲线数据
2. 将曲线数据与眼动分析结果结合
3. 在Maya中创建基于曲线的眼动动画
"""

import sys
from maya import cmds
from maya import OpenMaya

# 导入相关模块
from ..bezier_curve_editor import BezierCurveWidget, BezierPoint
from .curve_eyeball_integration import CurveEyeballIntegration
from .analysis_eyeball_rotation import AnalysisEyeballRotation
from .maya_usage_example import get_object_matrices_from_timeline


def create_sample_curve_widget():
    """
    创建一个示例贝塞尔曲线编辑器，包含预设的眨眼曲线
    
    Returns:
        BezierCurveWidget: 配置好的曲线编辑器
    """
    # 创建曲线编辑器
    curve_widget = BezierCurveWidget()
    
    # 清除默认关键帧
    curve_widget.keyframes.clear()
    
    # 创建眨眼曲线：睁眼(1) -> 闭眼(0) -> 睁眼(1)
    # 时间范围：0-10，值范围：0-1
    curve_widget.time_range = (0.0, 10.0)
    curve_widget.value_range = (0.0, 1.0)
    
    # 添加关键帧
    # 开始：睁眼状态
    kf1 = BezierPoint(time=0.0, value=1.0, in_tangent=(0.0, 0.0), out_tangent=(2.0, 0.0))
    
    # 中间：闭眼状态
    kf2 = BezierPoint(time=5.0, value=0.0, in_tangent=(-2.0, 0.0), out_tangent=(2.0, 0.0))
    
    # 结束：睁眼状态
    kf3 = BezierPoint(time=10.0, value=1.0, in_tangent=(-2.0, 0.0), out_tangent=(0.0, 0.0))
    
    curve_widget.keyframes = [kf1, kf2, kf3]
    
    print("创建了示例眨眼曲线：")
    print("  帧 0.0: 值 1.0 (睁眼)")
    print("  帧 5.0: 值 0.0 (闭眼)")
    print("  帧 10.0: 值 1.0 (睁眼)")
    
    return curve_widget


def create_test_matrix_data():
    """
    创建测试用的矩阵数据，模拟眼球旋转
    
    Returns:
        dict: {frame: OpenMaya.MMatrix} 测试矩阵数据
    """
    frame_matrix_dict = {}
    
    # 创建一系列有旋转变化的矩阵，模拟眨眼动作
    import math
    
    for frame in range(1, 21):  # 20帧数据
        if 5 <= frame <= 8:  # 在第5-8帧模拟眨眼动作
            # 创建有明显旋转的矩阵
            angle = math.radians(30 + (frame - 5) * 10)  # 30-60度旋转
            cos_a = math.cos(angle)
            sin_a = math.sin(angle)
            
            matrix = OpenMaya.MMatrix([
                1, 0, 0, 0,
                0, cos_a, -sin_a, 0,
                0, sin_a, cos_a, 0,
                0, 0, 0, 1
            ])
        else:
            # 正常状态，无旋转
            matrix = OpenMaya.MMatrix([
                1, 0, 0, 0,
                0, 1, 0, 0,
                0, 0, 1, 0,
                0, 0, 0, 1
            ])
        
        frame_matrix_dict[frame] = matrix
    
    print(f"创建了测试矩阵数据，共 {len(frame_matrix_dict)} 帧")
    print("模拟眨眼动作在第5-8帧")
    
    return frame_matrix_dict


def example_curve_data_extraction():
    """
    示例1：从曲线编辑器提取数据
    """
    print("\n=== 示例1：曲线数据提取 ===")
    
    # 创建示例曲线
    curve_widget = create_sample_curve_widget()
    
    # 创建集成器
    integrator = CurveEyeballIntegration(curve_widget)
    
    # 提取不同类型的曲线数据
    print("\n1. 原始曲线数据：")
    original_data = integrator.extract_curve_data(num_samples=10, normalized=False, reversed=False)
    if original_data:
        print(f"  时间范围: {original_data['time_range']}")
        print(f"  值范围: {original_data['value_range']}")
        print(f"  前5个数据点: {original_data['curve_data'][:5]}")
    
    print("\n2. 归一化曲线数据 (0-1)：")
    normalized_data = integrator.extract_curve_data(num_samples=10, normalized=True, reversed=False)
    if normalized_data:
        print(f"  时间范围: {normalized_data['time_range']}")
        print(f"  值范围: {normalized_data['value_range']}")
        print(f"  前5个数据点: {normalized_data['curve_data'][:5]}")
    
    print("\n3. 反转曲线数据 (1-0)：")
    reversed_data = integrator.extract_curve_data(num_samples=10, normalized=True, reversed=True)
    if reversed_data:
        print(f"  时间范围: {reversed_data['time_range']}")
        print(f"  值范围: {reversed_data['value_range']}")
        print(f"  前5个数据点: {reversed_data['curve_data'][:5]}")
    
    return integrator


def example_eyeball_analysis_integration():
    """
    示例2：眼动分析与曲线数据集成
    """
    print("\n=== 示例2：眼动分析与曲线集成 ===")
    
    # 创建曲线编辑器和集成器
    curve_widget = create_sample_curve_widget()
    integrator = CurveEyeballIntegration(curve_widget)
    
    # 创建测试矩阵数据
    frame_matrix_dict = create_test_matrix_data()
    
    # 执行眼动分析
    analyzer = AnalysisEyeballRotation()
    analyzer.frame_matrix_dict = frame_matrix_dict
    analyzer.frame_spacing = 2
    analyzer.angle_threshold = 15
    
    print("\n执行眼动分析...")
    analysis_result = analyzer.analyze_rotation_changes()
    
    # 提取曲线数据
    print("\n提取曲线数据...")
    curve_data = integrator.extract_curve_data(num_samples=50, normalized=True, reversed=False)
    
    if analysis_result and curve_data:
        print(f"\n眼动检测到 {len(analysis_result)} 个标记")
        print(f"曲线包含 {len(curve_data['values_only'])} 个采样点")
        
        # 显示分析结果
        print("\n眼动分析结果:")
        for frame in sorted(analysis_result.keys()):
            marker_type = "眨眼开始/完成" if analysis_result[frame] == 0 else "眨眼结束"
            print(f"  帧 {frame}: {analysis_result[frame]} ({marker_type})")
    
    return integrator, analysis_result, curve_data


def example_maya_integration():
    """
    示例3：在Maya中应用曲线数据到眼动动画
    
    注意：这个示例需要在Maya环境中运行
    """
    print("\n=== 示例3：Maya中的曲线眼动动画 ===")
    
    # 检查是否在Maya环境中
    try:
        selected_objects = cmds.ls(selection=True)
    except:
        print("此示例需要在Maya环境中运行")
        return None
    
    if not selected_objects:
        print("请先在Maya中选择一个对象（如眼皮对象）")
        return None
    
    object_name = selected_objects[0]
    print(f"使用对象: {object_name}")
    
    # 创建曲线编辑器和集成器
    curve_widget = create_sample_curve_widget()
    integrator = CurveEyeballIntegration(curve_widget)
    
    # 获取对象的矩阵数据（从时间轴）
    start_frame = int(cmds.playbackOptions(query=True, minTime=True))
    end_frame = int(cmds.playbackOptions(query=True, maxTime=True))
    
    print(f"分析时间范围: {start_frame} - {end_frame}")
    
    try:
        # 获取对象的矩阵数据
        frame_matrix_dict = get_object_matrices_from_timeline(object_name, start_frame, end_frame)
        
        if not frame_matrix_dict:
            print("无法获取对象的矩阵数据，使用测试数据")
            frame_matrix_dict = create_test_matrix_data()
        
        # 执行完整的集成流程
        result = integrator.create_eyeball_animation_with_curve(
            object_name=object_name,
            frame_matrix_dict=frame_matrix_dict,
            frame_spacing=2,
            angle_threshold=15,
            num_curve_samples=50,
            normalized=True,
            reversed=False,  # 设置为True可以反转曲线
            attribute_name="scaleY"  # 可以改为其他属性如rotateX
        )
        
        # 打印摘要
        integrator.print_integration_summary(result)
        
        print(f"\n✅ 成功为对象 '{object_name}' 创建了基于曲线的眼动动画")
        print("可以播放时间轴查看效果")
        
        return result
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
        return None


def example_custom_curve():
    """
    示例4：创建自定义曲线并应用
    """
    print("\n=== 示例4：自定义曲线应用 ===")
    
    # 创建自定义曲线编辑器
    curve_widget = BezierCurveWidget()
    curve_widget.keyframes.clear()
    curve_widget.time_range = (0.0, 20.0)
    curve_widget.value_range = (0.0, 1.0)
    
    # 创建更复杂的眨眼曲线：快速闭眼，慢速睁眼
    keyframes = [
        BezierPoint(time=0.0, value=1.0, in_tangent=(0.0, 0.0), out_tangent=(1.0, 0.0)),    # 睁眼
        BezierPoint(time=2.0, value=0.0, in_tangent=(-3.0, 0.0), out_tangent=(0.5, 0.0)),  # 快速闭眼
        BezierPoint(time=8.0, value=0.1, in_tangent=(-0.5, 0.0), out_tangent=(1.0, 0.0)),  # 微睁
        BezierPoint(time=20.0, value=1.0, in_tangent=(-2.0, 0.0), out_tangent=(0.0, 0.0))  # 慢速睁眼
    ]
    
    curve_widget.keyframes = keyframes
    
    # 创建集成器并提取数据
    integrator = CurveEyeballIntegration(curve_widget)
    
    # 比较正常和反转曲线
    normal_data = integrator.extract_curve_data(num_samples=20, normalized=True, reversed=False)
    reversed_data = integrator.extract_curve_data(num_samples=20, normalized=True, reversed=True)
    
    print("自定义曲线数据对比：")
    print("时间点 | 正常曲线 | 反转曲线")
    print("-" * 30)
    
    if normal_data and reversed_data:
        for i in range(min(10, len(normal_data['values_only']))):  # 显示前10个点
            normal_val = normal_data['values_only'][i]
            reversed_val = reversed_data['values_only'][i]
            time_val = normal_data['curve_data'][i][0]
            print(f"{time_val:6.2f} | {normal_val:8.3f} | {reversed_val:8.3f}")
    
    return integrator


def run_all_examples():
    """
    运行所有示例
    """
    print("🚀 开始运行贝塞尔曲线与眼动分析集成示例")
    print("=" * 60)
    
    # 示例1：曲线数据提取
    integrator1 = example_curve_data_extraction()
    
    # 示例2：眼动分析集成
    integrator2, analysis_result, curve_data = example_eyeball_analysis_integration()
    
    # 示例3：Maya集成（仅在Maya环境中）
    maya_result = example_maya_integration()
    
    # 示例4：自定义曲线
    integrator4 = example_custom_curve()
    
    print("\n" + "=" * 60)
    print("✅ 所有示例运行完成")
    
    if maya_result:
        print("💡 提示：可以在Maya中播放时间轴查看眼动动画效果")
    else:
        print("💡 提示：在Maya中选择对象后运行example_maya_integration()可以创建实际动画")


if __name__ == "__main__":
    # 如果直接运行此文件，执行所有示例
    run_all_examples()
