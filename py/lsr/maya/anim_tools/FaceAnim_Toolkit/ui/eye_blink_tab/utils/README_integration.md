# 贝塞尔曲线与眼动分析集成功能

这个集成功能将贝塞尔曲线编辑器的曲线数据与眼动分析功能结合，实现了您要求的功能：从曲线编辑器获取所有帧构成的曲线，映射到0-1范围，并支持反转到1-0范围。

## 主要功能

### 1. 曲线数据提取
- **原始曲线数据**: 获取曲线的原始时间和值
- **归一化数据**: 将曲线映射到0-1范围
- **反转数据**: 将曲线反转到1-0范围
- **值数组**: 仅获取曲线值，不包含时间信息

### 2. 眼动分析集成
- 将曲线数据应用到眼动检测结果
- 支持在眨眼周期中插入曲线数据
- 自动处理眨眼开始、结束和完成状态

### 3. Maya集成
- 直接导出曲线数据到Maya对象
- 支持多种属性（scaleY, rotateX等）
- 自动创建关键帧动画

## 文件结构

```
utils/
├── curve_eyeball_integration.py    # 主要集成模块
├── curve_integration_example.py    # 使用示例
├── test_integration.py            # 测试脚本
└── README_integration.md          # 本文档
```

## 使用方法

### 方法1: 通过贝塞尔曲线编辑器UI

1. **打开贝塞尔曲线编辑器**:
   ```python
   from bezier_curve_editor import BezierCurveEditor
   editor = BezierCurveEditor()
   editor.show()
   ```

2. **创建眨眼曲线**:
   - 点击"眨眼动画"预设按钮
   - 或手动创建：睁眼(1) -> 闭眼(0) -> 睁眼(1)

3. **配置集成设置**:
   - 采样点数：控制曲线精度（默认100）
   - 归一化：映射到0-1范围
   - 反转：创建1-0范围的反转曲线
   - 属性：选择要应用的Maya属性

4. **导出到Maya**:
   - 在Maya中选择目标对象（如眼皮）
   - 点击"导出到Maya"按钮
   - 播放时间轴查看效果

### 方法2: 通过代码调用

```python
# 1. 创建曲线编辑器
from bezier_curve_editor import BezierCurveWidget, BezierPoint
curve_widget = BezierCurveWidget()

# 2. 设置眨眼曲线
curve_widget.keyframes = [
    BezierPoint(time=0.0, value=1.0, out_tangent=(2.0, 0.0)),  # 睁眼
    BezierPoint(time=3.0, value=0.0, in_tangent=(-2.0, 0.0), out_tangent=(2.0, 0.0)),  # 闭眼
    BezierPoint(time=10.0, value=1.0, in_tangent=(-2.0, 0.0))  # 睁眼
]

# 3. 提取曲线数据
from curve_eyeball_integration import CurveEyeballIntegration
integrator = CurveEyeballIntegration(curve_widget)

# 获取归一化数据
normal_data = integrator.extract_curve_data(num_samples=100, normalized=True, reversed=False)

# 获取反转数据
reversed_data = integrator.extract_curve_data(num_samples=100, normalized=True, reversed=True)

# 4. 应用到Maya（需要在Maya环境中）
from maya import cmds
selected_object = cmds.ls(selection=True)[0]

# 应用正常曲线
integrator.apply_curve_to_eyeball_analysis(
    analysis_result={10: 0, 15: 1, 20: 0},  # 眨眼标记
    curve_data_dict=normal_data,
    attribute_name="scaleY",
    object_name=selected_object
)
```

### 方法3: 完整的眼动分析+曲线集成

```python
# 1. 准备眼动分析数据
from analysis_eyeball_rotation import AnalysisEyeballRotation
from maya_usage_example import get_object_matrices_from_timeline

# 获取对象的矩阵数据
object_name = "eyeball_L"
frame_matrix_dict = get_object_matrices_from_timeline(object_name, 1, 100)

# 2. 创建集成器并执行完整流程
integrator = CurveEyeballIntegration(curve_widget)
result = integrator.create_eyeball_animation_with_curve(
    object_name="eyelid_L",
    frame_matrix_dict=frame_matrix_dict,
    frame_spacing=2,
    angle_threshold=15,
    num_curve_samples=100,
    normalized=True,
    reversed=False,  # 设置为True可以反转曲线
    attribute_name="scaleY"
)

# 3. 查看结果摘要
integrator.print_integration_summary(result)
```

## 曲线数据格式

### 提取的数据结构
```python
{
    'curve_data': [(time, value), ...],  # 完整曲线数据点
    'values_only': [value1, value2, ...],  # 仅值数组
    'time_range': (start, end),  # 时间范围
    'value_range': (min, max),  # 值范围
    'settings': {  # 设置信息
        'num_samples': int,
        'normalized': bool,
        'reversed': bool
    }
}
```

### 数据映射说明
- **原始数据**: 保持曲线的原始时间和值范围
- **归一化数据**: 时间和值都映射到0-1范围
- **反转数据**: 在归一化基础上，值变为 1-normalized_value

## 应用场景

### 1. 眨眼动画
```python
# 正常眨眼：睁眼(1) -> 闭眼(0) -> 睁眼(1)
normal_blink = integrator.extract_curve_data(normalized=True, reversed=False)

# 反向眨眼：闭眼(0) -> 睁眼(1) -> 闭眼(0)
reverse_blink = integrator.extract_curve_data(normalized=True, reversed=True)
```

### 2. 不同属性应用
```python
# 缩放动画（眼皮厚度）
editor.export_curve_data_to_maya(attribute_name="scaleY")

# 旋转动画（眼皮角度）
editor.export_curve_data_to_maya(attribute_name="rotateX")

# 可见性动画
editor.export_curve_data_to_maya(attribute_name="visibility")
```

### 3. 精度控制
```python
# 低精度（快速预览）
low_res = integrator.extract_curve_data(num_samples=20)

# 高精度（最终渲染）
high_res = integrator.extract_curve_data(num_samples=200)
```

## 测试和验证

运行测试脚本验证功能：
```python
from test_integration import run_all_tests
run_all_tests()
```

运行示例：
```python
from curve_integration_example import run_all_examples
run_all_examples()
```

## 注意事项

1. **Maya环境**: 某些功能需要在Maya环境中运行
2. **对象选择**: 导出到Maya时需要先选择目标对象
3. **属性兼容**: 确保目标对象具有指定的属性
4. **曲线质量**: 采样点数影响动画精度和性能
5. **时间范围**: 确保曲线的时间范围与动画需求匹配

## 扩展功能

这个集成系统支持进一步扩展：
- 添加更多曲线预设
- 支持多对象批量应用
- 集成到更大的面部动画工具链
- 添加实时预览功能
