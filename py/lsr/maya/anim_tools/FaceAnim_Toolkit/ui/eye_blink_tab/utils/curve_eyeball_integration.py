# -*- coding: utf-8 -*-

"""
贝塞尔曲线编辑器与眼动分析集成模块

这个模块提供了将贝塞尔曲线编辑器的曲线数据与眼动分析功能结合的接口。
主要功能：
1. 从贝塞尔曲线编辑器提取曲线数据
2. 将曲线数据映射到0-1范围
3. 提供反转曲线数据（1-0范围）
4. 与analysis_eyeball_rotation集成，应用曲线数据到眼动检测结果
"""

import copy
from maya import cmds
from .analysis_eyeball_rotation import AnalysisEyeballRotation


class CurveEyeballIntegration(object):
    """贝塞尔曲线与眼动分析集成类"""
    
    def __init__(self, bezier_curve_widget=None):
        """
        初始化集成器
        
        Args:
            bezier_curve_widget: BezierCurveWidget实例
        """
        self.curve_widget = bezier_curve_widget
        self.eyeball_analyzer = None
        
    def set_curve_widget(self, curve_widget):
        """设置贝塞尔曲线编辑器"""
        self.curve_widget = curve_widget
        
    def set_eyeball_analyzer(self, analyzer):
        """设置眼动分析器"""
        self.eyeball_analyzer = analyzer
        
    def extract_curve_data(self, num_samples=100, normalized=True, reversed=False):
        """
        从曲线编辑器提取曲线数据
        
        Args:
            num_samples (int): 采样点数量
            normalized (bool): 是否归一化到0-1范围
            reversed (bool): 是否反转曲线（1-0范围）
            
        Returns:
            dict: {
                'curve_data': [(time, value), ...],  # 完整曲线数据
                'values_only': [value1, value2, ...],  # 仅值数组
                'time_range': (start, end),  # 时间范围
                'value_range': (min, max),  # 值范围
                'settings': {  # 设置信息
                    'num_samples': int,
                    'normalized': bool,
                    'reversed': bool
                }
            }
        """
        if not self.curve_widget or not self.curve_widget.keyframes:
            return None
            
        # 获取曲线数据
        if reversed:
            curve_data = self.curve_widget.get_reversed_curve_data(num_samples)
        elif normalized:
            curve_data = self.curve_widget.get_normalized_curve_data(num_samples)
        else:
            curve_data = self.curve_widget.get_curve_data(num_samples)
            
        if not curve_data:
            return None
            
        # 提取值数组
        values_only = [point[1] for point in curve_data]
        
        # 计算范围
        times = [point[0] for point in curve_data]
        values = [point[1] for point in curve_data]
        time_range = (min(times), max(times))
        value_range = (min(values), max(values))
        
        return {
            'curve_data': curve_data,
            'values_only': values_only,
            'time_range': time_range,
            'value_range': value_range,
            'settings': {
                'num_samples': num_samples,
                'normalized': normalized,
                'reversed': reversed
            }
        }
    
    def apply_curve_to_eyeball_analysis(self, analysis_result, curve_data_dict, 
                                      attribute_name="scaleY", object_name=None):
        """
        将曲线数据应用到眼动分析结果
        
        Args:
            analysis_result (dict): 眼动分析结果 {frame: marker}
            curve_data_dict (dict): 曲线数据字典（从extract_curve_data获取）
            attribute_name (str): 要应用的属性名
            object_name (str): Maya对象名，如果为None则使用选中对象
            
        Returns:
            dict: 应用了曲线数据的关键帧信息
        """
        if not analysis_result or not curve_data_dict:
            print("缺少分析结果或曲线数据")
            return {}
            
        # 获取对象名
        if object_name is None:
            selected_objects = cmds.ls(selection=True)
            if not selected_objects:
                print("请选择一个对象或指定object_name参数")
                return {}
            object_name = selected_objects[0]
            
        curve_values = curve_data_dict['values_only']
        settings = curve_data_dict['settings']
        
        print(f"应用曲线数据到对象: {object_name}")
        print(f"曲线设置: 采样点={settings['num_samples']}, 归一化={settings['normalized']}, 反转={settings['reversed']}")
        
        # 保存当前时间
        current_time = cmds.currentTime(query=True)
        applied_keyframes = {}
        
        try:
            # 分析眨眼周期并应用曲线数据
            frames_sorted = sorted(analysis_result.items())
            
            for i, (frame, marker) in enumerate(frames_sorted):
                if marker == 0:  # 眨眼开始或周期完成
                    # 判断是眨眼开始还是周期完成
                    if i > 0 and frames_sorted[i-1][1] == 1:
                        # 眨眼周期完成 - 应用曲线结束值
                        curve_value = curve_values[-1] if curve_values else 1.0
                        marker_type = "眨眼周期完成"
                    else:
                        # 眨眼开始 - 应用曲线开始值
                        curve_value = curve_values[0] if curve_values else 0.0
                        marker_type = "眨眼开始"
                        
                        # 如果有下一个标记（眨眼结束），在两个标记之间插入曲线数据
                        if i + 1 < len(frames_sorted):
                            next_frame, next_marker = frames_sorted[i + 1]
                            if next_marker == 1:  # 下一个是眨眼结束
                                self._apply_curve_between_frames(
                                    object_name, attribute_name, 
                                    frame, next_frame, curve_values, applied_keyframes
                                )
                                continue  # 跳过当前帧的单独处理
                                
                elif marker == 1:  # 眨眼结束
                    curve_value = curve_values[-1] if curve_values else 1.0
                    marker_type = "眨眼结束"
                
                # 设置关键帧
                cmds.currentTime(frame)
                cmds.setAttr(f'{object_name}.{attribute_name}', curve_value)
                cmds.setKeyframe(f'{object_name}.{attribute_name}')
                
                applied_keyframes[frame] = {
                    'value': curve_value,
                    'type': marker_type,
                    'original_marker': marker
                }
                
                print(f"  帧 {frame}: {attribute_name} = {curve_value:.3f} ({marker_type})")
                
        finally:
            # 恢复原始时间
            cmds.currentTime(current_time)
            
        print(f"曲线数据应用完成，共设置 {len(applied_keyframes)} 个关键帧")
        return applied_keyframes
    
    def _apply_curve_between_frames(self, object_name, attribute_name, 
                                  start_frame, end_frame, curve_values, applied_keyframes):
        """
        在两个帧之间应用曲线数据
        
        Args:
            object_name (str): Maya对象名
            attribute_name (str): 属性名
            start_frame (int): 开始帧
            end_frame (int): 结束帧
            curve_values (list): 曲线值列表
            applied_keyframes (dict): 应用的关键帧记录
        """
        frame_count = end_frame - start_frame + 1
        
        for i in range(frame_count):
            current_frame = start_frame + i
            
            # 计算在曲线中的位置
            if frame_count > 1:
                curve_index = int((i / (frame_count - 1)) * (len(curve_values) - 1))
            else:
                curve_index = 0
                
            curve_index = min(curve_index, len(curve_values) - 1)
            curve_value = curve_values[curve_index]
            
            # 设置关键帧
            cmds.currentTime(current_frame)
            cmds.setAttr(f'{object_name}.{attribute_name}', curve_value)
            cmds.setKeyframe(f'{object_name}.{attribute_name}')
            
            # 记录关键帧信息
            if i == 0:
                marker_type = "眨眼开始(曲线)"
            elif i == frame_count - 1:
                marker_type = "眨眼结束(曲线)"
            else:
                marker_type = "眨眼中间(曲线)"
                
            applied_keyframes[current_frame] = {
                'value': curve_value,
                'type': marker_type,
                'curve_index': curve_index,
                'frame_progress': i / (frame_count - 1) if frame_count > 1 else 0.0
            }
            
            print(f"    帧 {current_frame}: {attribute_name} = {curve_value:.3f} ({marker_type}, 曲线索引={curve_index})")
    
    def create_eyeball_animation_with_curve(self, object_name, frame_matrix_dict, 
                                          frame_spacing=2, angle_threshold=15,
                                          num_curve_samples=100, normalized=True, reversed=False,
                                          attribute_name="scaleY"):
        """
        完整的眼动分析+曲线应用流程
        
        Args:
            object_name (str): Maya对象名
            frame_matrix_dict (dict): 帧矩阵数据
            frame_spacing (int): 帧间隔
            angle_threshold (float): 角度阈值
            num_curve_samples (int): 曲线采样点数
            normalized (bool): 是否归一化曲线
            reversed (bool): 是否反转曲线
            attribute_name (str): 属性名
            
        Returns:
            dict: {
                'analysis_result': dict,  # 眼动分析结果
                'curve_data': dict,  # 曲线数据
                'applied_keyframes': dict  # 应用的关键帧
            }
        """
        # 1. 执行眼动分析
        if not self.eyeball_analyzer:
            self.eyeball_analyzer = AnalysisEyeballRotation()
            
        self.eyeball_analyzer.frame_matrix_dict = frame_matrix_dict
        self.eyeball_analyzer.frame_spacing = frame_spacing
        self.eyeball_analyzer.angle_threshold = angle_threshold
        
        analysis_result = self.eyeball_analyzer.analyze_rotation_changes()
        
        # 2. 提取曲线数据
        curve_data = self.extract_curve_data(num_curve_samples, normalized, reversed)
        
        # 3. 应用曲线到眼动分析结果
        applied_keyframes = self.apply_curve_to_eyeball_analysis(
            analysis_result, curve_data, attribute_name, object_name
        )
        
        return {
            'analysis_result': analysis_result,
            'curve_data': curve_data,
            'applied_keyframes': applied_keyframes
        }
    
    def print_integration_summary(self, result_dict):
        """
        打印集成结果摘要
        
        Args:
            result_dict (dict): create_eyeball_animation_with_curve的返回结果
        """
        print("\n=== 曲线眼动集成摘要 ===")
        
        analysis_result = result_dict.get('analysis_result', {})
        curve_data = result_dict.get('curve_data', {})
        applied_keyframes = result_dict.get('applied_keyframes', {})
        
        print(f"眼动检测标记: {len(analysis_result)}")
        if curve_data:
            settings = curve_data.get('settings', {})
            print(f"曲线采样点: {settings.get('num_samples', 0)}")
            print(f"曲线归一化: {settings.get('normalized', False)}")
            print(f"曲线反转: {settings.get('reversed', False)}")
            
        print(f"应用的关键帧: {len(applied_keyframes)}")
        
        if analysis_result:
            print("\n眼动检测结果:")
            for frame in sorted(analysis_result.keys()):
                marker_type = "眨眼开始/完成" if analysis_result[frame] == 0 else "眨眼结束"
                print(f"  帧 {frame}: {analysis_result[frame]} ({marker_type})")
