# -*- coding: utf-8 -*-

"""
简化版贝塞尔曲线编辑器测试脚本

测试修改后的功能：
1. 简化的UI（去掉时间范围、值范围控制和关键帧属性编辑栏）
2. 曲线采样功能
3. 归一化和反转功能
4. 入切线是否正确影响曲线形状
"""

import sys
import os

# 添加路径以便导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_curve_sampling():
    """测试曲线采样功能"""
    print("=== 测试曲线采样功能 ===")
    
    try:
        from bezier_curve_editor import BezierCurveWidget, BezierPoint
        
        # 创建曲线编辑器
        curve_widget = BezierCurveWidget()
        curve_widget.keyframes.clear()
        
        # 创建测试曲线，特别测试入切线的影响
        curve_widget.time_range = (0.0, 10.0)
        curve_widget.value_range = (0.0, 1.0)
        
        # 创建一个有明显入切线影响的曲线
        keyframes = [
            BezierPoint(time=0.0, value=0.0, in_tangent=(0.0, 0.0), out_tangent=(1.0, 0.0)),    # 起点
            BezierPoint(time=5.0, value=1.0, in_tangent=(-2.0, 1.0), out_tangent=(2.0, -1.0)), # 中点，有明显入切线
            BezierPoint(time=10.0, value=0.0, in_tangent=(-1.0, 0.0), out_tangent=(0.0, 0.0))  # 终点
        ]
        curve_widget.keyframes = keyframes
        
        print("创建了测试曲线，包含明显的入切线影响")
        print("关键帧信息:")
        for i, kf in enumerate(keyframes):
            print(f"  关键帧{i}: 时间={kf.time}, 值={kf.value}, 入切线={kf.in_tangent}, 出切线={kf.out_tangent}")
        
        # 测试不同的采样方法
        print("\n1. 原始曲线数据 (10个采样点):")
        original_data = curve_widget.get_curve_data(num_samples=10)
        for i, (time, value) in enumerate(original_data):
            print(f"  点{i}: 时间={time:.3f}, 值={value:.3f}")
        
        print("\n2. 归一化曲线数据 (10个采样点):")
        normalized_data = curve_widget.get_normalized_curve_data(num_samples=10)
        for i, (time, value) in enumerate(normalized_data):
            print(f"  点{i}: 时间={time:.3f}, 值={value:.3f}")
        
        print("\n3. 反转曲线数据 (10个采样点):")
        reversed_data = curve_widget.get_reversed_curve_data(num_samples=10)
        for i, (time, value) in enumerate(reversed_data):
            print(f"  点{i}: 时间={time:.3f}, 值={value:.3f}")
        
        print("\n4. 仅值数组 (归一化):")
        values_only = curve_widget.get_curve_values_only(num_samples=10, normalized=True, reversed=False)
        print(f"  值数组: {[f'{v:.3f}' for v in values_only]}")
        
        print("\n5. 仅值数组 (反转):")
        reversed_values = curve_widget.get_curve_values_only(num_samples=10, normalized=True, reversed=True)
        print(f"  反转值数组: {[f'{v:.3f}' for v in reversed_values]}")
        
        # 验证入切线是否影响曲线
        print("\n6. 验证入切线影响:")
        print("检查中点附近的曲线值变化...")
        
        # 在中点前后采样更多点
        detailed_samples = []
        for i in range(21):  # 从4.0到6.0，步长0.1
            t = 4.0 + i * 0.1
            value = curve_widget.evaluate_curve(t)
            detailed_samples.append((t, value))
        
        print("中点(5.0)附近的详细采样:")
        for t, v in detailed_samples[8:13]:  # 显示4.8到5.2的范围
            print(f"  时间={t:.1f}, 值={v:.3f}")
        
        print("✅ 曲线采样功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 曲线采样功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_tangent_influence():
    """专门测试入切线对曲线的影响"""
    print("\n=== 测试入切线影响 ===")
    
    try:
        from bezier_curve_editor import BezierCurveWidget, BezierPoint
        
        # 创建两个相同的曲线，但入切线不同
        curve1 = BezierCurveWidget()
        curve1.keyframes.clear()
        curve1.time_range = (0.0, 10.0)
        curve1.value_range = (0.0, 1.0)
        
        curve2 = BezierCurveWidget()
        curve2.keyframes.clear()
        curve2.time_range = (0.0, 10.0)
        curve2.value_range = (0.0, 1.0)
        
        # 曲线1：没有入切线影响
        keyframes1 = [
            BezierPoint(time=0.0, value=0.0, in_tangent=(0.0, 0.0), out_tangent=(1.0, 0.0)),
            BezierPoint(time=5.0, value=1.0, in_tangent=(0.0, 0.0), out_tangent=(0.0, 0.0)),  # 无入切线
            BezierPoint(time=10.0, value=0.0, in_tangent=(-1.0, 0.0), out_tangent=(0.0, 0.0))
        ]
        curve1.keyframes = keyframes1
        
        # 曲线2：有明显入切线影响
        keyframes2 = [
            BezierPoint(time=0.0, value=0.0, in_tangent=(0.0, 0.0), out_tangent=(1.0, 0.0)),
            BezierPoint(time=5.0, value=1.0, in_tangent=(-3.0, 2.0), out_tangent=(0.0, 0.0)),  # 强入切线
            BezierPoint(time=10.0, value=0.0, in_tangent=(-1.0, 0.0), out_tangent=(0.0, 0.0))
        ]
        curve2.keyframes = keyframes2
        
        print("比较两条曲线在中点附近的差异:")
        print("曲线1: 中点无入切线")
        print("曲线2: 中点有强入切线 (-3.0, 2.0)")
        
        print("\n中点前后的采样对比:")
        print("时间   | 曲线1值 | 曲线2值 | 差异")
        print("-" * 40)
        
        for i in range(11):
            t = 4.0 + i * 0.2  # 从4.0到6.0
            v1 = curve1.evaluate_curve(t)
            v2 = curve2.evaluate_curve(t)
            diff = abs(v2 - v1)
            print(f"{t:5.1f} | {v1:7.3f} | {v2:7.3f} | {diff:6.3f}")
        
        # 检查是否有明显差异
        max_diff = 0
        for i in range(21):
            t = 4.0 + i * 0.1
            v1 = curve1.evaluate_curve(t)
            v2 = curve2.evaluate_curve(t)
            max_diff = max(max_diff, abs(v2 - v1))
        
        print(f"\n最大差异: {max_diff:.3f}")
        
        if max_diff > 0.1:
            print("✅ 入切线正确影响曲线形状")
            return True
        else:
            print("❌ 入切线似乎没有影响曲线形状")
            return False
        
    except Exception as e:
        print(f"❌ 入切线影响测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ui_simplification():
    """测试UI简化"""
    print("\n=== 测试UI简化 ===")
    
    try:
        from bezier_curve_editor import BezierCurveEditor
        
        # 创建编辑器
        editor = BezierCurveEditor()
        
        # 检查是否有不应该存在的UI元素
        missing_elements = []
        
        # 检查是否移除了时间范围控制
        if hasattr(editor, 'time_start_spinbox'):
            missing_elements.append("时间范围控制未移除")
        
        if hasattr(editor, 'value_min_spinbox'):
            missing_elements.append("值范围控制未移除")
        
        if hasattr(editor, 'properties_widget'):
            missing_elements.append("关键帧属性面板未移除")
        
        # 检查是否保留了应该存在的元素
        required_elements = []
        
        if hasattr(editor, 'curve_widget'):
            required_elements.append("曲线编辑器")
        
        if hasattr(editor, 'sample_spinbox'):
            required_elements.append("采样控制")
        
        if hasattr(editor, 'normalize_checkbox'):
            required_elements.append("归一化选项")
        
        if hasattr(editor, 'reverse_checkbox'):
            required_elements.append("反转选项")
        
        print(f"保留的必要元素: {required_elements}")
        
        if missing_elements:
            print(f"❌ UI简化不完整: {missing_elements}")
            return False
        else:
            print("✅ UI简化成功")
            return True
        
    except Exception as e:
        print(f"❌ UI简化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行简化版贝塞尔曲线编辑器测试")
    print("=" * 60)
    
    tests = [
        ("曲线采样功能", test_curve_sampling),
        ("入切线影响", test_tangent_influence),
        ("UI简化", test_ui_simplification)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！简化版编辑器功能正常")
    else:
        print("⚠️  部分测试失败，请检查相关功能")
    
    print("\n💡 主要功能:")
    print("  - 曲线采样: curve_widget.get_curve_data(num_samples)")
    print("  - 归一化: curve_widget.get_normalized_curve_data(num_samples)")
    print("  - 反转: curve_widget.get_reversed_curve_data(num_samples)")
    print("  - 仅值数组: curve_widget.get_curve_values_only(num_samples, normalized, reversed)")


if __name__ == "__main__":
    run_all_tests()
