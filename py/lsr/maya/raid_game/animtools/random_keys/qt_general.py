# -*- coding: utf-8 -*-

# Import third-party modules
# from Qt import QtWidgets
from lsr.qt.core import QtWidgets

def mainwindow(widget=None):
    widget = widget or QtWidgets.QApplication.activeWindow()
    if widget is None:
        return

    parent = widget.parent()
    if parent is None:
        return widget

    return mainwindow(parent)


def get_app(object_name):
    q_app = QtWidgets.QApplication.instance()
    try:
        win_app = [x for x in q_app.topLevelWidgets() if x.objectName() == object_name][0]
    except IndexError:
        win_app = None
    return win_app


def launch_app(app_cls, object_name=None, is_show=True, **kwargs):
    q_app = QtWidgets.QApplication.instance()
    try:
        if object_name is None:
            object_name = app_cls.object_name
        pub_win = [x for x in q_app.topLevelWidgets() if x.objectName() == object_name][0]
    except IndexError:
        pub_win = app_cls(parent=mainwindow(), **kwargs)
        pub_win.setObjectName(object_name)
    if is_show:
        pub_win.show()
    return pub_win
