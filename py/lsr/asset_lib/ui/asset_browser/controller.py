import shutil
import os
import six
from functools import partial

from Qt import QtWidgets, QtCore, QtGui
# ui elements
from lsr.asset_lib.ui.asset_browser.view import AssetLibAssetBrowser, AssetLibBrowserSearchBar, AssetLibTagCtrl
from lsr.asset_lib.ui.asset_browser.model import AssetBrowserModel
from lsr.asset_lib.ui.asset_browser.delegate import CustomItemDelegate
from lsr.asset_lib.ui.widget.toast import AnimLibToast
from lsr.asset_lib.ui.sub_ui.pose_creation_dialog import launch as launch_pose_creation_dialog
from lsr.asset_lib.ui.sub_ui.asset_navigation_bar import AssetNavigationBar
# view style elements
from lsr.asset_lib.utility.pyside import resolve_icon_path
from lsr.asset_lib.utility.pyside import get_scale_factor
from lsr.asset_lib.utility.pyside import resolve_icon_path, copy_maya_font_style
from lsr.asset_lib.utility.utils import ramp_input
# data elements
from lsr.asset_lib.data import var_global
from lsr.asset_lib.manager.signal_manager import SignalManager
from lsr.asset_lib.manager.io_manager import IOManager, dcc_file_action, dcc
from lsr.asset_lib.manager.asset_cache_manager import AssetCacheManager
# action elements


_scale_facter, _ = get_scale_factor()


class AssetCollectorAddDialog(QtWidgets.QDialog):
    """ A dialog for adding an item to a collector. """

    def __init__(self, collector_name_list=None, parent=None):
        super(AssetCollectorAddDialog, self).__init__(parent)
        self.setParent(parent)
        self.setWindowTitle('Add item')
        self.collector_name = None
        self.collector_index = None
        self.collector_name_list = collector_name_list

        self._init_layout()
        self._do_signal_slot_connection()

    def _init_layout(self, *args, **kwargs):
        # widgets
        self.tips_lab = QtWidgets.QLabel('Collector Name', self)
        self.collector_list = QtWidgets.QListWidget(self)
        self.button_box = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok |
                                                     QtWidgets.QDialogButtonBox.Cancel,
                                                     QtCore.Qt.Horizontal, self)

        # preset widgets
        if self.collector_name_list:
            self.collector_list.addItems(self.collector_name_list)

        # layout
        main_lay = QtWidgets.QVBoxLayout(self)
        main_lay.addWidget(self.tips_lab)
        main_lay.addWidget(self.collector_list)
        main_lay.addWidget(self.button_box)

    def _do_signal_slot_connection(self, *args, **kwargs):
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)

    def accept(self, *args, **kwargs):
        """ if accepted, the parameter will be the index of the selected collector.  """
        result_item = self.collector_list.currentItem()
        result_index = self.collector_list.currentRow()
        if not result_item:
            self.collector_name = None
            self.collector_index = None
            return AnimLibToast.error(text='No collector was selected!', parent=self)
        else:
            self.collector_name = result_item.text()
            self.collector_index = result_index
        return super(AssetCollectorAddDialog, self).accept()

    def reject(self, *args, **kwargs):
        """ if rejected, the parameter will be None. """
        self.collector_name = None
        self.collector_index = None
        return super(AssetCollectorAddDialog, self).reject()

    def exec_(self):
        """ rewrite the exec_ method to return the result. """
        super(AssetCollectorAddDialog, self).exec_()
        return self.collector_index, self.collector_name

    def closeEvent(self, event, *args, **kwargs):
        """ rewrite the closeEvent method to remove the dialog from the parent widget.  """
        super(AssetCollectorAddDialog, self).closeEvent(event)
        self.setParent(None)
        self.destroy()


class AssetCollectorEditingDialog(QtWidgets.QDialog):
    """ A title input dialog for creating collector """

    def __init__(self, parent=None, data=None):
        super(AssetCollectorEditingDialog, self).__init__(parent)
        self.setParent(parent)
        self.setWindowTitle('Collector edit')
        self.collector_list = list()

        self._init_layout()
        self._do_signal_slot_connection()

        if data:
            for name in data:
                self.item_list.addItem(name)

    def _init_layout(self, *args, **kwargs):
        # widgets
        self.tips_lab = QtWidgets.QLabel('Collector list', self)
        self.item_list = QtWidgets.QListWidget(self)
        self.add_btn = QtWidgets.QPushButton('Add', self)
        self.remove_btn = QtWidgets.QPushButton('Remove', self)
        self.edit_btn = QtWidgets.QPushButton('Edit', self)
        self.button_box = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok |
                                                     QtWidgets.QDialogButtonBox.Cancel,
                                                     QtCore.Qt.Horizontal, self)

        # layout
        list_lay = QtWidgets.QVBoxLayout()
        list_lay.setAlignment(QtCore.Qt.AlignTop)
        edit_btn_lay = QtWidgets.QVBoxLayout()
        edit_btn_lay.setAlignment(QtCore.Qt.AlignTop)
        base_part_lay = QtWidgets.QHBoxLayout()

        # list part
        list_lay.addWidget(self.tips_lab)
        list_lay.addWidget(self.item_list)

        # edit button part
        edit_btn_lay.addSpacing(self.tips_lab.height())
        edit_btn_lay.addWidget(self.add_btn)
        edit_btn_lay.addWidget(self.edit_btn)
        edit_btn_lay.addSpacing(50)
        edit_btn_lay.addWidget(self.remove_btn)

        # base function part
        base_part_lay.addLayout(list_lay)
        base_part_lay.addLayout(edit_btn_lay)

        main_lay = QtWidgets.QVBoxLayout(self)
        main_lay.addLayout(base_part_lay)
        main_lay.addWidget(self.button_box)

    def _do_signal_slot_connection(self, *args, **kwargs):
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)

        self.add_btn.clicked.connect(self.add_new_item)
        self.remove_btn.clicked.connect(self.remove_current_item)
        self.edit_btn.clicked.connect(self.edit_current_item)

    def _call_line_dialog(self):
        """ Build a instance of enter line dialog  """
        line_dialog = QtWidgets.QDialog(self)
        main_lay = QtWidgets.QVBoxLayout(line_dialog)

        tips_lab = QtWidgets.QLabel('Collector name', line_dialog)
        enter_edit = QtWidgets.QLineEdit(line_dialog)
        button_box = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok |
                                                QtWidgets.QDialogButtonBox.Cancel,
                                                QtCore.Qt.Horizontal, line_dialog)

        # preset widgets
        enter_edit.setPlaceholderText('Enter collector name in here...')

        # layout
        main_lay.addWidget(tips_lab)
        main_lay.addWidget(enter_edit)
        main_lay.addWidget(button_box)

        # signal slot connection
        button_box.accepted.connect(line_dialog.accept)
        button_box.rejected.connect(line_dialog.reject)

        if line_dialog.exec_():
            result = enter_edit.text()
        else:
            result = None

        line_dialog.setParent(None)
        line_dialog.destroy()
        return result

    def add_new_item(self):
        """ add new item to list """
        collector_name = self._call_line_dialog()
        if collector_name:
            name = collector_name
            counter = 1

            check_name_list = [self.item_list.item(index).text() for index in range(self.item_list.count())]
            while name in check_name_list:
                name = "{bar_name} ({counter})".format(bar_name=collector_name, counter=counter)
                counter += 1
            self.item_list.addItem(name)

    def remove_current_item(self):
        self.item_list.takeItem(self.item_list.currentRow())

    def edit_current_item(self):
        collector_name = self._call_line_dialog()
        if collector_name:
            self.item_list.currentItem().setText(collector_name)

    def exec_(self):
        super(AssetCollectorEditingDialog, self).exec_()
        for i in range(self.item_list.count()):
            self.collector_list.append(self.item_list.item(i).text())
        return self.collector_list

    def paintEvent(self, event):
        """ rewrite the paint event to set button """
        if self.item_list.selectedIndexes():
            self.edit_btn.setEnabled(True)
            self.remove_btn.setEnabled(True)
        else:
            self.edit_btn.setEnabled(False)
            self.remove_btn.setEnabled(False)
        return super(AssetCollectorEditingDialog, self).paintEvent(event)

    def closeEvent(self, event, *args, **kwargs):
        super(AssetCollectorEditingDialog, self).closeEvent(event)
        self.setParent(None)
        self.destroy()


class AssetLibBrowserSortController(QtWidgets.QWidget):
    """ A sort bar for sorting animation clips and poses. """
    def __init__(self, parent=None):
        super(AssetLibBrowserSortController, self).__init__(parent)

        self._init_layout()
        self._do_signal_connection()

    def _init_layout(self, *args, **kwargs):
        main_lay = QtWidgets.QHBoxLayout(self)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_lay.addSpacing(10)

        # sort widget
        self.sort_by_option = QtWidgets.QComboBox(self)
        self.sort_by_option.addItems(["Name", "Date"])
        self.sort_by_label = QtWidgets.QLabel("Sort By : ")
        self.sort_btn_grp = QtWidgets.QButtonGroup(self)
        self.sort_ascending = QtWidgets.QRadioButton("Ascending")
        self.sort_descending = QtWidgets.QRadioButton("Descending")
        self.sort_btn_grp.addButton(self.sort_ascending, 0)
        self.sort_btn_grp.addButton(self.sort_descending, 1)
        self.search_bar = AssetLibBrowserSearchBar(self)
        self.tag_control = AssetLibTagCtrl(self)

        # preset ascending sort
        self.sort_ascending.setChecked(True)

        # layout
        sort_by_lay = QtWidgets.QHBoxLayout()
        sort_by_lay.setSpacing(0)
        sort_by_lay.setContentsMargins(0, 0, 0, 0)
        sort_by_lay.addWidget(self.sort_by_label)
        sort_by_lay.addWidget(self.sort_by_option)
        sort_by_lay.addSpacing(10)
        sort_by_lay.addWidget(self.sort_ascending)
        sort_by_lay.addSpacing(10)
        sort_by_lay.addWidget(self.sort_descending)

        main_lay.addLayout(sort_by_lay)
        main_lay.addStretch()
        main_lay.addWidget(self.search_bar)
        main_lay.addStretch()
        main_lay.addWidget(self.tag_control)
        main_lay.addSpacing(10)

    def _do_signal_connection(self):
        self.search_bar.search_bar_edit.textChanged.connect(partial(SignalManager.view_filter_active.emit))
        for checker in self.tag_control.checker_list:
            checker.clicked.connect(partial(SignalManager.view_filter_active.emit, None))


class AssetCollectorController(QtWidgets.QWidget):

    """ A widget for collecting assets in the asset browser """
    def __init__(self, parent=None):
        super(AssetCollectorController, self).__init__(parent)
        self._init_layout()
        self._customized_view()
        self._do_signal_connection()
        self.collector_file_paths = dict()
        self.clean_up()

    def _init_layout(self, *args, **kwargs):
        # path button part
        self.backup_btn = QtWidgets.QPushButton(self)
        self.go_on_btn = QtWidgets.QPushButton(self)
        self.back_btn = QtWidgets.QPushButton(self)
        # button content
        self.btn_widget = QtWidgets.QWidget(self)
        self.btn_widget.setLayout(QtWidgets.QHBoxLayout())
        self.btn_widget.layout().addWidget(self.backup_btn)
        self.btn_widget.layout().addWidget(self.go_on_btn)
        self.btn_widget.layout().addSpacing(20)
        self.btn_widget.layout().addWidget(self.back_btn)
        self.btn_widget.layout().setSpacing(10)

        self.navigation_bar = AssetNavigationBar(self)

        # collector part
        text_lab = QtWidgets.QLabel('Filter : ', self)
        self.collector_cbbox = QtWidgets.QComboBox(self)
        self.add_tab_btn = QtWidgets.QPushButton(self)
        self.over_show_cb = QtWidgets.QCheckBox("Overwrite Show", self)
        # collector content
        self.filter_widget = QtWidgets.QWidget(self)
        self.filter_widget.setLayout(QtWidgets.QHBoxLayout())
        self.filter_widget.layout().addWidget(text_lab)
        self.filter_widget.layout().addWidget(self.collector_cbbox)
        self.filter_widget.layout().addWidget(self.add_tab_btn)

        # layout
        _main_lay = QtWidgets.QHBoxLayout(self)
        _main_lay.setAlignment(QtCore.Qt.AlignCenter | QtCore.Qt.AlignLeft)
        self.setLayout(_main_lay)
        _main_lay.addWidget(self.btn_widget)
        _main_lay.addWidget(self.navigation_bar)
        _main_lay.addWidget(self.filter_widget)
        _main_lay.addWidget(self.over_show_cb)
        _main_lay.setContentsMargins(0, 0, 0, 0)
        _main_lay.setSpacing(0)

    def _do_signal_connection(self):
        self.add_tab_btn.clicked.connect(partial(self.call_edit_dialog))
        self.backup_btn.clicked.connect(partial(self._slot_backup_last_path))
        self.go_on_btn.clicked.connect(partial(self._slot_go_to_next_path))
        self.back_btn.clicked.connect(partial(self.slot_back_to_previous))
        self.over_show_cb.stateChanged.connect(partial(self.slot_overwrite_show_changed))

        self.navigation_bar.jump_to_signal.connect(partial(SignalManager.jump_to_folder.emit))

    def _customized_view(self):
        self.setFixedHeight(self.collector_cbbox.height() + 20)
        self.add_tab_btn.setIcon(QtGui.QIcon(resolve_icon_path("/library/gear_write.png")))
        self.add_tab_btn.setStyleSheet("""border: none; background-color: #31363b;""")
        self.add_tab_btn.setIconSize(QtCore.QSize(15 * _scale_facter, 15 * _scale_facter))
        self.back_btn.setIcon(QtGui.QIcon(resolve_icon_path("/library/arrowup.png")))
        self.back_btn.setIconSize(QtCore.QSize(15 * _scale_facter, 15 * _scale_facter))

    def _widget_shadow_control(self, *args, **kwargs):
        """ manage the shadow effect view of the widget """
        horizontal_scrollbar = self.navigation_bar.horizontalScrollBar()
        if horizontal_scrollbar.maximum() != 0:
            if horizontal_scrollbar.value() == horizontal_scrollbar.minimum():
                shadow_effect_star = None
                shadow_effect_end = QtWidgets.QGraphicsDropShadowEffect(self)
                shadow_effect_end.setOffset(0, 0)
                shadow_effect_end.setColor(QtCore.Qt.black)
                shadow_effect_end.setBlurRadius(20)
            elif horizontal_scrollbar.value() == horizontal_scrollbar.maximum():
                shadow_effect_star = QtWidgets.QGraphicsDropShadowEffect(self)
                shadow_effect_star.setOffset(0, 0)
                shadow_effect_star.setColor(QtCore.Qt.black)
                shadow_effect_star.setBlurRadius(20)
                shadow_effect_end = None
            else:
                shadow_effect_star = QtWidgets.QGraphicsDropShadowEffect(self)
                shadow_effect_star.setOffset(0, 0)
                shadow_effect_star.setColor(QtCore.Qt.black)
                shadow_effect_star.setBlurRadius(20)
                shadow_effect_end = QtWidgets.QGraphicsDropShadowEffect(self)
                shadow_effect_end.setOffset(0, 0)
                shadow_effect_end.setColor(QtCore.Qt.black)
                shadow_effect_end.setBlurRadius(20)
        else:
            shadow_effect_star = None
            shadow_effect_end = None

        self.btn_widget.setGraphicsEffect(shadow_effect_star)
        self.filter_widget.setGraphicsEffect(shadow_effect_end)

        self.navigation_bar.set_scroll_bar_to_end()

    def slot_overwrite_show_changed(self, *args, **kwargs):
        """ The overwrite show option changed signal. """
        if args[0] == QtCore.Qt.Checked:
            var_global.ASSET_OVERWRITE_SHOW = True
        else:
            var_global.ASSET_OVERWRITE_SHOW = False
        SignalManager.resource_refresh.emit()


    @staticmethod
    def _slot_backup_last_path(*args, **kwargs):
        AssetCacheManager.back_to_path()
        SignalManager.jump_to_folder.emit(var_global.ASSET_CURRENT_PATH_LIST[-1])

    @staticmethod
    def _slot_go_to_next_path(*args, **kwargs):
        next_path = var_global.ASSET_MEMORY_PATH_LIST[len(var_global.ASSET_CURRENT_PATH_LIST)]
        AssetCacheManager.go_to_path(next_path)
        SignalManager.jump_to_folder.emit(var_global.ASSET_CURRENT_PATH_LIST[-1])

    @staticmethod
    def slot_back_to_previous(*args, **kwargs):
        real_path = var_global.ASSETS_CURRENT_FOLDER
        dir_path = os.path.dirname(real_path)
        SignalManager.jump_to_folder.emit(dir_path)

    def set_collector_data(self, cache_data):
        """ Set the data for the collector. """
        old_name = self.collector_cbbox.currentText()
        if six.PY2:
            if type(old_name) == unicode:
                old_name = old_name.encode("gb2312")
        self.clean_up()
        for collector_name, paths_list in cache_data.items():
            self.collector_file_paths[collector_name] = paths_list
        self.update_collector_combo_box()

        for index in range(self.collector_cbbox.count()):
            new_name = self.collector_cbbox.itemText(index)
            if six.PY2:
                if type(new_name) == unicode:
                    new_name = new_name.encode("gb2312")
            if new_name == old_name:
                self.collector_cbbox.setCurrentIndex(index)
                return

    def clean_up(self):
        self.collector_cbbox.blockSignals(True)
        self.collector_cbbox.clear()
        self.collector_cbbox.addItem('Default')
        self.collector_file_paths = dict()
        self.collector_cbbox.blockSignals(False)

    def call_edit_dialog(self):
        old_dict = self.collector_file_paths
        self.clean_up()
        _data = AssetCollectorEditingDialog(var_global.INSTANCE_WINDOW_ROOT, data=old_dict.keys()).exec_()
        new_dict = dict()
        for name in _data:
            if name in old_dict.keys():
                new_dict[name] = old_dict[name]
            else:
                new_dict[name] = list()
        self.collector_file_paths = new_dict
        self.update_collector_combo_box()
        self.update_collector_cache()

    def update_collector_combo_box(self):
        old_name = self.collector_cbbox.currentText()
        name_list = self.collector_file_paths.keys()
        for name in name_list:
            self.collector_cbbox.addItem(name)
        self.collector_cbbox.setCurrentText(old_name)

    def update_collector_cache(self):
        var_global.ASSETS_CURRENT_SPACE_CACHE['collectors_part'] = self.collector_file_paths
        AssetCacheManager.save_cache(var_global.ASSETS_CURRENT_SPACE_CACHE)

    def paintEvent(self, event):
        """ set buttons enabled or not by cache """
        if len(var_global.ASSET_CURRENT_PATH_LIST) <= 1:
            self.backup_btn.setEnabled(False)
        else:
            self.backup_btn.setEnabled(True)

        if var_global.ASSET_CURRENT_PATH_LIST == var_global.ASSET_MEMORY_PATH_LIST:
            self.go_on_btn.setEnabled(False)
        else:
            self.go_on_btn.setEnabled(True)
        self._widget_shadow_control()
        return super(AssetCollectorController, self).paintEvent(event)


class AssetsBrowserController(QtWidgets.QWidget):
    def __init__(self, current_path=None, parent=None, *args, **kwargs):
        super(AssetsBrowserController, self).__init__(parent)
        self._init_layout()
        self._init_content_menu()
        self._do_signal_connection()
        self.current_resource_change(current_path)

    def _init_layout(self, *args, **kwargs):
        main_lay = QtWidgets.QVBoxLayout(self)
        main_lay.setSpacing(0)

        # init base widget
        self.sort_controller = AssetLibBrowserSortController(self)
        self.collect_controller = AssetCollectorController(self)
        self.view = AssetLibAssetBrowser(self)
        self.model = AssetBrowserModel(self)

        # preset view and model
        self.view.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.view.setModel(self.model)
        self.delegate = CustomItemDelegate(self.view)
        self.view.setItemDelegate(self.delegate)

        # layout
        main_lay.addWidget(self.sort_controller)
        main_lay.addWidget(self.collect_controller)
        main_lay.addWidget(self.view)

        # shortcut
        self.copy_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence("Ctrl+C"), self)
        self.cut_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence("Ctrl+X"), self)
        self.paste_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence("Ctrl+V"), self)
        self.delete_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence("Delete"), self)

    def _init_content_menu(self):
        """ Collecting all the actions for the right click menu. """
        self.right_click_menu = QtWidgets.QMenu(self)
        self.right_click_menu.setParent(self)

        self.switch_view = QtWidgets.QAction('Switch View Mode', self.right_click_menu)
        self.file_save_as_here = QtWidgets.QAction('Save as here', self.right_click_menu)
        self.paste_asset_action = QtWidgets.QAction('Paste', self.right_click_menu)

        # right click on item
        self.open_file_action = QtWidgets.QAction('Open', self.right_click_menu)
        self.create_new_folder_action = QtWidgets.QAction('Create New Folder', self.right_click_menu)
        self.import_file_action = QtWidgets.QAction('Import to Scene', self.right_click_menu)
        self.reference_file_action = QtWidgets.QAction('Create Reference', self.right_click_menu)
        self.preview_image_action = QtWidgets.QAction('Update Image from Scene', self.right_click_menu)
        self.preview_image_disk_action = QtWidgets.QAction('Update Image from Disk', self.right_click_menu)
        self.show_in_explorer_action = QtWidgets.QAction('Show In Explorer', self.right_click_menu)
        self.show_in_folder_view_action = QtWidgets.QAction('Show In Folder View', self.right_click_menu)
        self.copy_asset_action = QtWidgets.QAction('Copy', self.right_click_menu)
        self.cut_asset_action = QtWidgets.QAction('Cut', self.right_click_menu)
        self.rename_asset_action = QtWidgets.QAction('Rename', self.right_click_menu)
        self.delete_asset_action = QtWidgets.QAction('Delete', self.right_click_menu)
        self.copy_absolute_path = QtWidgets.QAction('Copy Absolute Path', self.right_click_menu)
        self.copy_relative_path = QtWidgets.QAction('Copy Relative Path', self.right_click_menu)
        self.add_to_collector_action = QtWidgets.QAction('Add to Collection', self.right_click_menu)
        self.remove_from_collector_action = QtWidgets.QAction('Remove Collection', self.right_click_menu)

        self.allways_show_filter_list = [self.switch_view,
                                         self.paste_asset_action,
                                         self.create_new_folder_action]

        self.action_on_item_filter_list = [self.open_file_action,
                                           self.import_file_action,
                                           self.reference_file_action,
                                           self.preview_image_action,
                                           self.preview_image_disk_action,
                                           self.show_in_explorer_action,
                                           self.show_in_folder_view_action,
                                           self.copy_asset_action,
                                           self.cut_asset_action,
                                           self.rename_asset_action,
                                           self.delete_asset_action,
                                           self.copy_absolute_path,
                                           self.copy_relative_path,
                                           self.add_to_collector_action,
                                           self.remove_from_collector_action]

        self.action_on_folder_filter_list = [self.open_file_action,
                                             self.preview_image_action,
                                             self.preview_image_disk_action,
                                             self.show_in_explorer_action,
                                             self.show_in_folder_view_action,
                                             self.copy_asset_action,
                                             self.cut_asset_action,
                                             self.rename_asset_action,
                                             self.delete_asset_action,
                                             self.copy_absolute_path,
                                             self.copy_relative_path,
                                             self.add_to_collector_action,
                                             self.remove_from_collector_action]

        self.action_on_nothing_filter_list = [self.file_save_as_here]

        # add action
        self.right_click_menu.addAction(self.switch_view)
        self.right_click_menu.addSeparator()
        self.right_click_menu.addAction(self.open_file_action)
        self.right_click_menu.addAction(self.create_new_folder_action)
        self.right_click_menu.addAction(self.import_file_action)
        self.right_click_menu.addAction(self.reference_file_action)
        self.right_click_menu.addAction(self.file_save_as_here)
        self.right_click_menu.addSeparator()
        self.right_click_menu.addAction(self.add_to_collector_action)
        self.right_click_menu.addAction(self.remove_from_collector_action)
        self.right_click_menu.addSeparator()
        self.right_click_menu.addAction(self.preview_image_action)
        self.right_click_menu.addAction(self.preview_image_disk_action)
        self.right_click_menu.addSeparator()
        self.right_click_menu.addAction(self.copy_absolute_path)
        self.right_click_menu.addAction(self.copy_relative_path)
        self.right_click_menu.addSeparator()
        self.right_click_menu.addAction(self.copy_asset_action)
        self.right_click_menu.addAction(self.cut_asset_action)
        self.right_click_menu.addAction(self.paste_asset_action)
        self.right_click_menu.addSeparator()
        self.right_click_menu.addAction(self.delete_asset_action)
        self.right_click_menu.addAction(self.rename_asset_action)
        self.right_click_menu.addSeparator()
        self.right_click_menu.addAction(self.show_in_folder_view_action)
        self.right_click_menu.addAction(self.show_in_explorer_action)

    def right_click_menu_action_filter(self):
        """ Filter the right click menu actions. """
        for action in self.right_click_menu.actions():
            if action.text():
                action.setVisible(False)

        for action in self.allways_show_filter_list:
            action.setVisible(True)

        if var_global.ASSETS_CURRENT_IO_DATA:
            if var_global.ASSETS_CURRENT_IO_DATA['item_tag'] == ['folder']:
                actions = self.action_on_folder_filter_list
            else:
                actions = self.action_on_item_filter_list
            for action in actions:
                action.setVisible(True)
                if self.collect_controller.collector_cbbox.currentIndex() == 0:
                    self.remove_from_collector_action.setVisible(False)
                else:
                    self.add_to_collector_action.setVisible(False)
        else:
            for action in self.action_on_nothing_filter_list:
                action.setVisible(True)

        if var_global.IO_CURRENT_ASSET_OPERATION_PATH:
            self.paste_asset_action.setEnabled(True)
        else:
            self.paste_asset_action.setEnabled(False)

        if dcc == 'mobu':
            self.reference_file_action.setVisible(False)
            self.import_file_action.setVisible(False)

    def _do_signal_connection(self, *args, **kwargs):
        # sort signals
        self.sort_controller.sort_by_option.currentTextChanged.connect(partial(self.slot_sort_by_changed))
        self.sort_controller.sort_ascending.clicked.connect(partial(self.slot_sort_by_btn_changed))
        self.sort_controller.sort_descending.clicked.connect(partial(self.slot_sort_by_btn_changed))

        # collector signals
        self.collect_controller.collector_cbbox.currentIndexChanged.connect(partial(self.collector_content_change))

        # view signals
        self.view.selectionModel().selectionChanged.connect(partial(self.update_current_io_data))

        # receive signals
        SignalManager.folder_selection_change.connect(partial(self.current_resource_change))
        SignalManager.view_filter_active.connect(partial(self.slot_filter_item))
        SignalManager.init_asset_collector.connect(partial(self.collector_tab_init))

        # icon refresh signal
        SignalManager.changed_file_browser_icon_size.connect(partial(self.change_item_size_cb))
        SignalManager.changed_file_browser_icon_space.connect(partial(self.change_item_space_cb))

        # -----------------------menu signals---------------------------
        # context menu signals
        self.view.customContextMenuRequested.connect(partial(self.show_context_menu))
        self.show_in_explorer_action.triggered.connect(partial(self.open_in_explorer))
        self.create_new_folder_action.triggered.connect(partial(self.create_new_folder))
        self.open_file_action.triggered.connect(partial(self.slot_open_file))
        self.reference_file_action.triggered.connect(partial(self.slot_create_reference))
        self.import_file_action.triggered.connect(partial(self.slot_import_file))
        self.file_save_as_here.triggered.connect(partial(self.slot_file_save_as))
        self.preview_image_action.triggered.connect(partial(self.slot_create_preview_image))
        self.preview_image_disk_action.triggered.connect(partial(self.slot_create_preview_image_from_disk))
        self.show_in_folder_view_action.triggered.connect(partial(self.slot_show_in_folder))
        self.copy_absolute_path.triggered.connect(partial(self.slot_copy_file_path, 'absolute'))
        self.copy_relative_path.triggered.connect(partial(self.slot_copy_file_path, 'relative'))
        self.copy_asset_action.triggered.connect(partial(self.slot_update_asset_operation, 'copy'))
        self.cut_asset_action.triggered.connect(partial(self.slot_update_asset_operation, 'cut'))
        self.paste_asset_action.triggered.connect(partial(self.slot_paste_operation))
        self.delete_asset_action.triggered.connect(partial(self.slot_delete_file))
        self.rename_asset_action.triggered.connect(partial(self.slot_rename_file))
        self.switch_view.triggered.connect(partial(self.view.switch_view_mode))
        self.add_to_collector_action.triggered.connect(partial(self.slot_add_to_collector))
        self.remove_from_collector_action.triggered.connect(partial(self.slot_remove_from_collector))

        self.copy_shortcut.activated.connect(partial(self.slot_update_asset_operation, 'copy'))
        self.cut_shortcut.activated.connect(partial(self.slot_update_asset_operation, 'cut'))
        self.paste_shortcut.activated.connect(partial(self.slot_paste_operation))
        self.delete_shortcut.activated.connect(partial(self.slot_delete_file))

        self.view.doubleClicked.connect(partial(self.slot_open_file))\

    def create_new_folder(self, *args, **kwargs):
        current_path = var_global.ASSETS_CURRENT_FOLDER
        text, ok = QtWidgets.QInputDialog.getText(self, 'Create Folder Dialog', 'Enter folder name:')
        if not ok:
            return
        folder_path = os.path.join(current_path, text)
        try:
            os.mkdir(folder_path)
        except BaseException as e:
            AnimLibToast.error(text=str(e),
                               parent=var_global.INSTANCE_WINDOW_ROOT)

    def change_item_space_cb(self, value, *args, **kwargs):
        item_space = ramp_input(value, var_global.STRUCTURE_ICONSPACE)
        self.view.setSpacing(item_space)

    def change_item_size_cb(self, value, *args, **kwargs):
        item_size = ramp_input(value, var_global.STRUCTURE_ICONSIZE) * var_global.UI_SCALE_FACTOR[0]
        self.view.setIconSize(QtCore.QSize(item_size, item_size))

    def slot_file_save_as(self, *args, **kwargs):
        """ Slot for file save as. """
        file_path = dcc_file_action.save_as_get_name(self)
        while file_path:
            if os.path.exists(file_path):
                question_result = QtWidgets.QMessageBox.question(self, "Warning", "File already exists! \nDo you want to overwrite it?")
                if question_result == QtWidgets.QMessageBox.Yes:
                    return IOManager.file_save_as(file_path)
                else:
                    base_name = os.path.splitext(os.path.basename(file_path))[0]
                    file_path = dcc_file_action.save_as_get_name(self, preset_name=base_name)
            else:
                return IOManager.file_save_as(file_path)

    def slot_rename_file(self, *args, **kwargs):
        """ rename file and deal the cache """
        index = self.model.asset_items.index(var_global.ASSET_CURRENT_ITEM)
        model_current_item = self.model.asset_items[index]

        # prepare old file param
        old_file_name = model_current_item.io_data['file_name']
        old_relative_file_path = model_current_item.io_data['file_path']
        old_full_file_path = os.path.join(var_global.RESOURCE_ROOT_PATH, old_relative_file_path)

        split_name = os.path.splitext(old_file_name)
        name_dialog = QtWidgets.QInputDialog(self)
        name_dialog.setLabelText("Rename : ")
        name_dialog.setTextValue(split_name[0])
        if name_dialog.exec_():
            new_file_name = name_dialog.textValue() + split_name[-1]
            if new_file_name != old_file_name:
                # file name overwrite check
                counter = 1
                _, extension = os.path.splitext(old_file_name)
                dir_path = os.path.dirname(old_full_file_path)
                dst_path = os.path.join(var_global.RESOURCE_ROOT_PATH,
                                        os.path.join(dir_path, new_file_name + extension))
                while os.path.exists(dst_path):
                    new_file_name = "{new_file_name} ({counter})".format(new_file_name=new_file_name, counter=counter)
                    dst_path = os.path.join(var_global.RESOURCE_ROOT_PATH,
                                            os.path.join(dir_path, new_file_name + extension))
                self._rename_io_data(model_current_item.io_data, new_file_name)

                # deal children's cache
                for root, dirs, files in os.walk(old_full_file_path):
                    # dirs
                    for dir_name in dirs:
                        entry_path = os.path.join(root, dir_name)
                        rel_path = os.path.relpath(entry_path, var_global.RESOURCE_ROOT_PATH)
                        io_data = self.model.collect_io_data(rel_path).io_data
                        old_path = os.path.relpath(old_full_file_path, var_global.RESOURCE_ROOT_PATH)
                        new_path = os.path.relpath(dst_path, var_global.RESOURCE_ROOT_PATH)
                        new_relative_path = io_data['file_path'].replace(old_path, new_path, 1)
                        self._edit_io_data_by_change(io_data, io_data['file_name'], new_relative_path)
                    # files
                    for file_name in files:
                        if os.path.splitext(file_name)[-1].lower() in ['.fbx', '.ma', '.mb']:
                            entry_path = os.path.join(root, file_name)
                            rel_path = os.path.relpath(entry_path, var_global.RESOURCE_ROOT_PATH)
                            io_data = self.model.collect_io_data(rel_path).io_data
                            old_path = os.path.relpath(old_full_file_path, var_global.RESOURCE_ROOT_PATH)
                            new_path = os.path.relpath(dst_path, var_global.RESOURCE_ROOT_PATH)
                            new_relative_path = io_data['file_path'].replace(old_path, new_path, 1)
                            self._edit_io_data_by_change(io_data, io_data['file_name'], new_relative_path)

                IOManager.rename_file(old_full_file_path, new_file_name)

    def _rename_io_data(self, io_data, new_file_name):
        dir_name = os.path.dirname(io_data['file_path'])
        relative_path = os.path.join(dir_name, new_file_name)
        self._edit_io_data_by_change(io_data, new_file_name, relative_path)

    def _move_io_data(self, io_data, new_dir_path):
        """ if new_file_name is not None, means it is a rename operation, or it is a file move operation """
        file_path = io_data['file_path']
        if new_dir_path == os.path.relpath(var_global.ASSETS_CURRENT_FOLDER, var_global.RESOURCE_ROOT_PATH):
            # ----------------------------------------------same name----------------------------------------------#
            # this is an example to show how to fix the same name issue when asset moving                          #
            #                                                                                                      #
            #                                                example                                               #
            #                                                                                                      #
            #    input: new_dir_path = a/b              new_dir_path and asset browser's current path are the same #
            #              file_name = a/b/c/file       some asset's file relative path                            #
            #              base_path = a/b              this part will not be change                               #
            #         remaining_path = c/file           this part is new moving path                               #
            #         replace_target = c                this is the new path need to fix the same name problem     #
            #    output:## file_path = a/b/c (counter part)/file                                                   #
            #                                                                                                      #
            #                                                                                                      #
            #                                                example2                                              #
            #                                                                                                      #
            #    input: new_dir_path = a/b              new_dir_path and asset browser's current path are the same #
            #              file_name = a/b/c/d/file     some asset's file relative path                            #
            #              base_path = a/b              this part will not be change                               #
            #         remaining_path = c/d/file         this part is new moving path                               #
            #         replace_target = c                this is the new path need to fix the same name problem     #
            #    output:## file_path = a/b/c (counter part)/d/file                                                 #
            #                                                                                                      #
            #                                                                                                      #
            #                                 example3 (root path to root path)                                    #
            #                                                                                                      #
            #    input: new_dir_path = '.'              root to root, so it will be '.',                           #
            #              file_name = a/b/c/d/file     some asset's file relative path                            #
            #              base_path = ''               empty string, because there's no parent folder for root dir#
            #         remaining_path = a/b/c/d/file     same with the file_name                                    #
            #         replace_target = a                first part is the part of same name problem we need to fix #
            #    output:## file_path = a (counter part)/b/c/d/file                                                 #
            #                                                                                                      #
            #                                                                                                      #
            if var_global.ASSETS_CURRENT_FOLDER == var_global.RESOURCE_ROOT_PATH:  # root path
                base_path = ''
                remaining_path = file_path
                replace_target = remaining_path.split("\\")[0]
            else:
                end_pos = file_path.find(new_dir_path) + len(new_dir_path)
                next_slash_pos = file_path.find('\\', end_pos)

                base_path = file_path[:next_slash_pos]
                remaining_path = file_path[next_slash_pos:]
                replace_target = remaining_path.split("\\")[1]

            counter = 1

            while os.path.exists(os.path.join(var_global.RESOURCE_ROOT_PATH, file_path)):
                new_remaining_path = remaining_path.replace(
                    replace_target,
                    "{replace_target} ({counter})".format(replace_target=replace_target,  counter=counter),
                    1)
                counter += 1
                file_path = base_path + new_remaining_path
            # --------------------------------------------same name end--------------------------------------------#

        file_name = os.path.basename(file_path)
        if var_global.ASSETS_CURRENT_FOLDER == var_global.RESOURCE_ROOT_PATH:
            replace_part = ''
            new_dir_path += '\\'
        else:
            replace_part = os.path.relpath(var_global.ASSETS_CURRENT_FOLDER, var_global.RESOURCE_ROOT_PATH)
        # replace the dir path to current path
        relative_path = file_path.replace(new_dir_path, replace_part, 1)

        self._edit_io_data_by_change(io_data, file_name, relative_path)

    def _edit_io_data_by_change(self, io_data, file_name, relative_path):
        if io_data in list(var_global.ASSETS_CURRENT_SPACE_CACHE['files_part'].values()):
            operation = True
        else:
            operation = False
        io_data['file_name'] = file_name
        io_data['file_path'] = relative_path
        io_data['asset_info']['File Name'] = file_name
        io_data['asset_info']['File Path'] = os.path.normpath(os.path.join(var_global.RESOURCE_ROOT_PATH,
                                                                           relative_path)).replace('\\', '/')
        self._deal_preview_image(io_data, relative_path)
        if operation:
            var_global.ASSETS_CURRENT_SPACE_CACHE['files_part'][relative_path] = io_data
            AssetCacheManager.save_cache(var_global.ASSETS_CURRENT_SPACE_CACHE)

    @staticmethod
    def _deal_preview_image(io_data, new_relative_path):
        if not any(io_data['icon_path'] == path for path in [
            resolve_icon_path(sub_dir='/library/fbx_icon.png'),
            resolve_icon_path(sub_dir='/library/maya_icon.png'),
            resolve_icon_path(sub_dir='/library/aslib_folder.png')]
                   ):
            pic_dir_name = os.path.dirname(io_data['icon_path'])
            pic_new_path = os.path.join(pic_dir_name,
                                        os.path.normpath(new_relative_path).replace('\\', '.') + '.0000.jpg')

            shutil.copy2(
                os.path.join(var_global.RESOURCE_ROOT_PATH, io_data['icon_path']),
                os.path.join(var_global.RESOURCE_ROOT_PATH, pic_new_path))
            io_data['icon_path'] = pic_new_path

    def collector_tab_init(self, tab_cache):
        """ Collector tab init. """
        self.collect_controller.set_collector_data(tab_cache)

    def collector_content_change(self, *args, **kwargs):
        """ Tab changed slot. """
        self.current_resource_change(var_global.ASSETS_CURRENT_FOLDER)

    def current_resource_change(self, current_path):
        """ Current folder changed signal. """
        if current_path:
            AssetCacheManager.go_to_path(current_path)
            # get selected index
            selected_index = self.view.selectedIndexes()

            # get io data for matching
            if selected_index:
                # if selected, get io data, else not
                old_item_iodata_list = [self.model.asset_items[index.row()].io_data for index in selected_index]
            else:
                old_item_iodata_list = None

            if var_global.ASSETS_CURRENT_IO_DATA:
                old_global_val = var_global.ASSETS_CURRENT_IO_DATA
            else:

                old_global_val = None

            # set path
            # if collector is not active, set path to current path
            collector_index = self.collect_controller.collector_cbbox.currentIndex()
            if collector_index == 0:
                self.model.set_data_path(current_path)
            # or set path to collected path
            else:
                collector_name = self.collect_controller.collector_cbbox.itemText(collector_index)
                new_paths = [path for path in self.collect_controller.collector_file_paths[collector_name]
                             if os.path.exists(path)]
                self.collect_controller.collector_file_paths[collector_name] = new_paths
                self.collect_controller.update_collector_cache()
                self.model.set_data_path(current_path, new_paths)
            partial(self.slot_sort_by_changed, self.sort_controller.sort_by_option.currentText())()

            # if item was selected at before, select it again
            if var_global.PASTE_NEED_SELECT_RELPATH_LIST:
                old_item_iodata_list = [item.io_data for item in self.model.asset_items
                                        if item.io_data['file_path'] in var_global.PASTE_NEED_SELECT_RELPATH_LIST]
                var_global.PASTE_NEED_SELECT_RELPATH_LIST = None
            if old_item_iodata_list:
                start_index = self.model.index(0, 0)
                matching_indexes = list()
                for old_item_iodata in old_item_iodata_list:
                    matching_indexes += self.model.match(start_index, QtCore.Qt.UserRole, old_item_iodata, 1, QtCore.Qt.MatchExactly)
                if matching_indexes:
                    for index in matching_indexes:
                        self.view.selectionModel().select(index, QtCore.QItemSelectionModel.Select)
                if old_global_val:
                    old_index = self.model.match(start_index, QtCore.Qt.UserRole, old_global_val, 1, QtCore.Qt.MatchExactly)
                    var_global.ASSET_CURRENT_ITEM = self.model.asset_items[old_index[0].row()] if old_index else None
                    var_global.ASSETS_CURRENT_IO_DATA = self.model.asset_items[old_index[0].row()].io_data if old_index else None
            self.collect_controller.navigation_bar.refresh_navigation_bar(var_global.RESOURCE_ROOT_PATH,
                                                                          var_global.ASSETS_CURRENT_FOLDER)
        else:
            self.model.cleanup()
        self.slot_filter_item()

    def call_collector_add_dialog(self):
        add_dialog = AssetCollectorAddDialog(self.collect_controller.collector_file_paths.keys(), self)
        return add_dialog.exec_()

    def slot_delete_file(self):
        """ Delete file from disk """
        indexes = [index.row() for index in self.view.selectedIndexes()]
        for index in indexes:
            file_real_path = os.path.join(var_global.RESOURCE_ROOT_PATH,
                                          self.model.asset_items[index].io_data['file_path'])
            IOManager.delete_file(file_real_path)

        SignalManager.resource_refresh.emit()

    def slot_update_asset_operation(self, method):
        """ Delete file from disk """
        indexes = [index.row() for index in self.view.selectedIndexes()]
        paths = list()
        items = list()
        for index in indexes:
            file_real_path = os.path.join(var_global.RESOURCE_ROOT_PATH,
                                          self.model.asset_items[index].io_data['file_path'])
            paths.append(file_real_path)
            items.append(self.model.asset_items[index])

        var_global.IO_CURRENT_ASSET_OPERATION_PATH = paths
        var_global.IO_CURRENT_ASSET_OPERATION_METHOD = method

    def slot_paste_operation(self):
        """ Paste operation. """
        # check path is legal
        result = IOManager.check_paste_operation_legal(var_global.IO_CURRENT_ASSET_OPERATION_PATH,
                                                       var_global.ASSETS_CURRENT_FOLDER)
        if result != 'Finish':
            AnimLibToast.error('Operation Stop!\n' + result, parent=var_global.INSTANCE_WINDOW_ROOT)
        else:
            # paste operation
            if var_global.ASSETS_CURRENT_FOLDER:
                # operating all selected items
                var_global.PASTE_NEED_SELECT_RELPATH_LIST = list()
                for path in var_global.IO_CURRENT_ASSET_OPERATION_PATH:
                    io_data = self.model.collect_io_data(os.path.relpath(path, var_global.RESOURCE_ROOT_PATH)).io_data
                    self._move_io_data(
                        io_data,
                        os.path.relpath(os.path.dirname(path), var_global.RESOURCE_ROOT_PATH))
                    var_global.PASTE_NEED_SELECT_RELPATH_LIST.append(io_data['file_path'])

                    # children paths operation
                    for root, dirs, files in os.walk(path):
                        # dirs
                        for dir_name in dirs:
                            entry_path = os.path.join(root, dir_name)
                            rel_path = os.path.relpath(entry_path, var_global.RESOURCE_ROOT_PATH)
                            io_data = self.model.collect_io_data(rel_path).io_data
                            self._move_io_data(
                                io_data,
                                os.path.relpath(os.path.dirname(path), var_global.RESOURCE_ROOT_PATH))
                        # files
                        for file_name in files:
                            if os.path.splitext(file_name)[-1].lower() in ['.fbx', '.ma', '.mb']:
                                entry_path = os.path.join(root, file_name)
                                rel_path = os.path.relpath(entry_path, var_global.RESOURCE_ROOT_PATH)
                                io_data = self.model.collect_io_data(rel_path).io_data
                                self._move_io_data(
                                    io_data,
                                    os.path.relpath(os.path.dirname(path), var_global.RESOURCE_ROOT_PATH))

                # do paste
                IOManager.paste_file(var_global.IO_CURRENT_ASSET_OPERATION_PATH,
                                     var_global.ASSETS_CURRENT_FOLDER,
                                     var_global.IO_CURRENT_ASSET_OPERATION_METHOD)
                AnimLibToast.success('Finish!', parent=var_global.INSTANCE_WINDOW_ROOT)

        var_global.IO_CURRENT_ASSET_OPERATION_PATH = None
        var_global.IO_CURRENT_ASSET_OPERATION_METHOD = None

        SignalManager.resource_refresh.emit()

    def slot_add_to_collector(self):
        """ Add item to collector. """
        if self.collect_controller.collector_cbbox.count() <= 1:
            AnimLibToast.error(text='No collector was created!', parent=var_global.INSTANCE_WINDOW_ROOT)
            return
        collector_index, collector_name = self.call_collector_add_dialog()
        if collector_name:
            path = var_global.ASSETS_CURRENT_IO_DATA['file_path']
            path = os.path.normpath(os.path.join(var_global.RESOURCE_ROOT_PATH, path))
            self.collect_controller.collector_file_paths[collector_name].append(path)
            self.collector_content_change()
            AnimLibToast.success(text='Finish!', parent=var_global.INSTANCE_WINDOW_ROOT)

    def slot_remove_from_collector(self):
        """ Add item to collector. """
        result_index = self.collect_controller.collector_cbbox.currentIndex()
        if result_index != 0:
            collector_name = self.collect_controller.collector_cbbox.currentText()
            path = var_global.ASSETS_CURRENT_IO_DATA['file_path']
            path = os.path.normpath(os.path.join(var_global.RESOURCE_ROOT_PATH, path))
            self.collect_controller.collector_file_paths[collector_name].remove(path)
            self.collector_content_change()
            AnimLibToast.success(text='Finish!', parent=var_global.INSTANCE_WINDOW_ROOT)

    @staticmethod
    def slot_copy_file_path(path_type, *args, **kwargs):
        """ Get and copy the file path to clipboard. """
        path = var_global.ASSETS_CURRENT_IO_DATA['file_path']
        if not path:
            return
        path = os.path.normpath(path)
        if path_type != 'relative':
            path = os.path.normpath(os.path.join(var_global.RESOURCE_ROOT_PATH, path))

        # copy to clipboard
        IOManager.copy_path(path)

    @staticmethod
    def slot_show_in_folder(*args, **kwargs):
        """ Show asset's path in folder view (not explorer). """
        real_path = os.path.join(var_global.RESOURCE_ROOT_PATH, var_global.ASSETS_CURRENT_IO_DATA['file_path'])
        dir_path = os.path.dirname(real_path)
        SignalManager.jump_to_folder.emit(dir_path)

    def slot_sort_by_btn_changed(self, *args, **kwargs):
        """ The sort by option changed signal. """
        self.slot_sort_by_changed(self.sort_controller.sort_by_option.currentText())

    def slot_sort_by_changed(self, *args, **kwargs):
        """ The sort by option changed signal. """
        pattern = args[0]
        self.model.sort_item(pattern, self.sort_controller.sort_btn_grp.checkedId())
        self.slot_filter_item()

    def slot_filter_item(self, *args, **kwargs):
        """ The filter item signal. """
        row_count = len(self.model.asset_items)
        if row_count:
            visible_row = [row for row in range(row_count)]

            # reset
            for row in visible_row:
                self.view.setRowHidden(row, False)

            # apply tags
            tags = self.sort_controller.tag_control.get_active_tags()
            need_hide_index = self.model.get_hidden_item_index_by_tags(tags)
            for row in need_hide_index:
                self.view.setRowHidden(row, True)
                visible_row.remove(row)

            # apply search_pattern
            pattern = self.sort_controller.search_bar.get_current_text()
            if pattern:
                keep_showing_index = self.model.get_hidden_item_index_by_filter_pattern(pattern)
                for row in visible_row:
                    if row not in keep_showing_index:
                        self.view.setRowHidden(row, True)
        if self.view.selectedIndexes():
            index = self.model.match(self.model.index(0, 0), QtCore.Qt.UserRole,
                                     var_global.ASSETS_CURRENT_IO_DATA, 1, QtCore.Qt.MatchExactly)
            if index:
                index = index[0]
        else:
            index = None
        self.update_current_io_data(index=index)

    def show_context_menu(self, pos):
        """ Show context menu when right click on folder view. """
        if self.view.indexAt(pos).isValid():
            self.update_current_io_data(index=self.view.indexAt(pos))
        point = QtCore.QPoint(pos.x() + 10, pos.y() + 10)
        self.right_click_menu_action_filter()
        self.right_click_menu.exec_(self.view.mapToGlobal(point))

    def open_in_explorer(self):
        """ Show current folder in explorer. """
        if var_global.ASSETS_CURRENT_IO_DATA:
            current_path = var_global.ASSETS_CURRENT_IO_DATA['file_path']
            if current_path:
                return IOManager.open_path(os.path.join(var_global.RESOURCE_ROOT_PATH, current_path))
        if var_global.ASSETS_CURRENT_FOLDER:
            return IOManager.open_path(var_global.ASSETS_CURRENT_FOLDER)
        return QtWidgets.QMessageBox.information(self, 'No selected', 'You have not selected any items.')

    def update_current_io_data(self, *args, **kwargs):
        """ Update current io data if there is a selected item. """
        selected_index = list()
        if 'index' in kwargs:
            if kwargs['index']:
                selected_index = [kwargs['index']]
        else:
            selected_index = self.view.selectedIndexes()
        if selected_index:
            var_global.ASSET_CURRENT_ITEM = self.model.asset_items[selected_index[0].row()]
            var_global.ASSETS_CURRENT_IO_DATA = self.model.asset_items[selected_index[0].row()].io_data
        else:
            var_global.ASSET_CURRENT_ITEM = None
            var_global.ASSETS_CURRENT_IO_DATA = None
        SignalManager.io_data_updated.emit()

    def slot_create_reference(self):
        """ Create reference for current asset. """
        if var_global.ASSETS_CURRENT_IO_DATA:
            file_path = os.path.join(var_global.RESOURCE_ROOT_PATH, var_global.ASSETS_CURRENT_IO_DATA['file_path'])
            dcc_file_action.file_reference(file_path)

    @staticmethod
    def slot_open_file(*args, **kwargs):
        """ Open current file in external editor. """
        current_io_data = var_global.ASSETS_CURRENT_IO_DATA
        if current_io_data:
            rel_path = os.path.normpath(os.path.join(var_global.RESOURCE_ROOT_PATH, current_io_data['file_path']))
            if os.path.isdir(rel_path):
                SignalManager.jump_to_folder.emit(rel_path)
            else:
                scene_modified = dcc_file_action.check_file_modify()
                file_path = os.path.join(var_global.RESOURCE_ROOT_PATH, var_global.ASSETS_CURRENT_IO_DATA['file_path'])
                if not scene_modified:
                    dcc_file_action.file_open(file_path)
                    return True
                else:
                    do_next = dcc_file_action.ask_save_current_scene()
                    if do_next:
                        dcc_file_action.file_open(file_path)
                        return True
                    return False

    @staticmethod
    def slot_import_file():
        """ Import current file to scene. """
        if var_global.ASSETS_CURRENT_IO_DATA:
            file_path = os.path.join(var_global.RESOURCE_ROOT_PATH, var_global.ASSETS_CURRENT_IO_DATA['file_path'])
            dcc_file_action.file_import(file_path)

    @staticmethod
    def slot_create_preview_image():
        """ Create preview image for current asset. """
        AssetCacheManager.prepare_update_pic()
        return launch_pose_creation_dialog(AssetCacheManager.update_cache)

    @staticmethod
    def slot_create_preview_image_from_disk():
        """ Create preview image for current asset from disk. """
        AssetCacheManager.prepare_update_pic()
        return AssetCacheManager.update_pic_from_disk()

    def paintEvent(self, event):
        """ set view if no resource path exist or hide collect controller."""
        if not var_global.RESOURCE_ROOT_PATH:
            if not self.collect_controller.isHidden():
                self.collect_controller.hide()
        else:
            if self.collect_controller.isHidden():
                self.collect_controller.show()

        if var_global.ASSETS_CURRENT_FOLDER == var_global.RESOURCE_ROOT_PATH:
            self.collect_controller.back_btn.setEnabled(False)
        else:
            self.collect_controller.back_btn.setEnabled(True)
        return super(AssetsBrowserController, self).paintEvent(event)


class LabelSlider(QtWidgets.QWidget):
    """
    [ text : -----|------ ]
    combination
    """

    def __init__(self):
        super(LabelSlider, self).__init__()
        self.slider = QtWidgets.QSlider(orientation=QtCore.Qt.Horizontal)
        self.slider.setValue(50)
        self.slider.setStyleSheet(
            'QSlider::handle:horizontal {border-radius: 0px; background-color: #232629;} QSlider:horizontal {background-color: #565a5e;}')

        self.text = QtWidgets.QLabel()
        self.text.setText("my test  :  ")
        self.layout = QtWidgets.QHBoxLayout()
        self.layout.addWidget(self.text)
        self.layout.addWidget(self.slider)
        self.setLayout(self.layout)

    def set_text(self, text):
        self.text.setText(text)

    def set_fixed_width(self, width):
        self.setFixedWidth(width)


class AssetLibFlipbookSliderCtrl(QtWidgets.QWidget):
    """Slider QWidget which control flipbook image size and space"""

    def __init__(self, parent=None, *args, **kwargs):
        super(AssetLibFlipbookSliderCtrl, self).__init__(parent)

        self._init_layout()
        self._customized_view()
        self._do_signal_slot_connection()

    def _init_layout(self, *args, **kwargs):

        restore_btn_size = (17, 17)

        # slider group
        layout_slider = QtWidgets.QHBoxLayout()
        layout_slider.setContentsMargins(0, 0, 0, 0)
        layout_slider.addStretch()
        layout_slider.setSpacing(10)
        layout_slider.setAlignment(QtCore.Qt.AlignRight)

        for i, lable in enumerate(["Space", "Size"]):
            slider = LabelSlider()
            slider.set_fixed_width(175)
            slider.set_text("%s  " % lable)
            cls_var = "slider_%s" % lable
            copy_maya_font_style(slider.text, size=8)
            setattr(self, cls_var, slider)

            reset_btn = self._create_reset_button(restore_btn_size)
            reset_btn.clicked.connect(partial(self.slot_reset_slider, slider))

            layout_00 = QtWidgets.QHBoxLayout()
            layout_00.setContentsMargins(0, 0, 0, 0)
            layout_00.setSpacing(0)
            # layout_00.setAlignment(QtCore.Qt.AlignHCenter)

            layout_01 = QtWidgets.QHBoxLayout()
            layout_01.setContentsMargins(0, 0, 0, 2)
            layout_01.setSpacing(0)
            layout_01.addWidget(reset_btn)

            layout_00.addWidget(slider)
            layout_00.addLayout(layout_01)
            layout_slider.addLayout(layout_00)


        # main layout
        self.main_layout = QtWidgets.QHBoxLayout()
        self.main_layout.setSpacing(0)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setAlignment(QtCore.Qt.AlignRight)
        self.main_layout.addLayout(layout_slider)
        self.setLayout(self.main_layout)

    def _customized_view(self, *args, **kwargs):
        self.setStyleSheet("border: none;background-color: transparent;")
        self.setFixedHeight(33)

    def _do_signal_slot_connection(self, *args, **kwargs):
        self.slider_Space.slider.valueChanged.connect(partial(self.slot_itemSpace_changed))
        self.slider_Size.slider.valueChanged.connect(partial(self.slot_itemSize_changed))

    def _create_reset_button(self, restore_btn_size, *args, **kwargs):
        pixmap = QtGui.QPixmap(
            resolve_icon_path("/library/rotate.png"))  # Change the path and size as needed
        mask = pixmap.mask()
        pixmap.fill(QtGui.QColor('#605f5f'))  # Change the color as needed
        pixmap.setMask(mask)
        reset_btn = QtWidgets.QPushButton()
        reset_btn.setFixedWidth(restore_btn_size[0])
        reset_btn.setFixedHeight(restore_btn_size[1])
        reset_btn.setIcon(QtGui.QIcon(pixmap))
        reset_btn.setIconSize(QtCore.QSize(restore_btn_size[0], restore_btn_size[1]))
        return reset_btn

    def slot_reset_slider(self, LabelSlider, *args, **kwargs):
        LabelSlider.slider.setValue(50)

    def slot_itemSpace_changed(self, value, *args, **kwargs):
        SignalManager.changed_file_browser_icon_space.emit(value)

    def slot_itemSize_changed(self, value, *args, **kwargs):
        SignalManager.changed_file_browser_icon_size.emit(value)


class AssetLibStatusController(QtWidgets.QWidget):
    """main bottom widget"""
    def __init__(self, parent=None):
        super(AssetLibStatusController, self).__init__(parent)
        self._init_layout()
        self._customized_view()

    def _init_layout(self, *args, **kwargs):
        self.slider_control = AssetLibFlipbookSliderCtrl()
        main_layout = QtWidgets.QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addStretch()
        main_layout.addStretch()
        main_layout.addWidget(self.slider_control)
        main_layout.setSpacing(20)
        self.setLayout(main_layout)

    def _customized_view(self, *args, **kwargs):
        pass

    def save_settings(self, *args, **kwargs):
        settings = var_global.INSTANCE_WINDOW_ROOT._qsettings
        settings.beginGroup('ui_setting')
        settings.setValue('item_size', self.slider_control.slider_Size.slider.value())
        settings.setValue('item_space', self.slider_control.slider_Space.slider.value())
        settings.endGroup()
        settings.sync()
        return settings

    def load_settings(self, *args, **kwargs):
        settings = var_global.INSTANCE_WINDOW_ROOT._qsettings
        settings.beginGroup('ui_setting')
        item_size = 0 if settings.value('item_size') == None else int(settings.value('item_size')),
        item_space = 0 if settings.value('item_space') == None else int(settings.value('item_space')),
        self.slider_control.slider_Size.slider.setValue(item_size[0])
        self.slider_control.slider_Space.slider.setValue(item_space[0])
        settings.endGroup()
        return settings

